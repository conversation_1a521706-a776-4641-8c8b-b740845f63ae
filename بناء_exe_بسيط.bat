@echo off
chcp 65001 >nul
title بناء exe بسيط - YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 بناء exe بسيط وسريع                       ║
echo ║              YouTube Video Downloader                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo 📦 تثبيت PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ فشل في تثبيت PyInstaller
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت المكتبات الأساسية...
pip install yt-dlp Pillow colorama

echo.
echo 🎨 إنشاء الشعارات...
if exist "create_logo.py" (
    python create_logo.py
)

echo.
echo 🔨 بناء الملف التنفيذي...
echo ⏳ انتظر من فضلك...

REM بناء بسيط بدون تعقيدات
pyinstaller --onefile --windowed --name "YouTube_Video_Downloader" gui_working.py

if errorlevel 1 (
    echo.
    echo ❌ فشل البناء الأول، جاري المحاولة بطريقة أخرى...
    
    REM محاولة بدون --windowed
    pyinstaller --onefile --name "YouTube_Video_Downloader_Console" gui_working.py
    
    if errorlevel 1 (
        echo ❌ فشل البناء نهائياً
        echo.
        echo 💡 جرب:
        echo    1. أعد تشغيل الكمبيوتر
        echo    2. شغّل كمدير
        echo    3. تأكد من مساحة القرص
        pause
        exit /b 1
    ) else (
        echo ✅ تم إنشاء نسخة كونسول
        set EXE_NAME=YouTube_Video_Downloader_Console.exe
    )
) else (
    echo ✅ تم البناء بنجاح!
    set EXE_NAME=YouTube_Video_Downloader.exe
)

echo.
echo 📊 التحقق من النتيجة...

if exist "dist\%EXE_NAME%" (
    echo ✅ الملف التنفيذي جاهز!
    echo 📁 المكان: dist\%EXE_NAME%
    
    REM حساب الحجم
    for %%A in ("dist\%EXE_NAME%") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo 📊 الحجم: !size_mb! MB
    )
    
    echo.
    echo 🎉 تم بناء البرنامج بنجاح!
    echo.
    echo هل تريد تشغيل البرنامج الآن؟ (y/n)
    set /p run_now="اختر: "
    
    if /i "!run_now!"=="y" (
        echo 🚀 تشغيل البرنامج...
        start "" "dist\%EXE_NAME%"
    )
    
    echo.
    echo هل تريد فتح مجلد الملف؟ (y/n)
    set /p open_folder="اختر: "
    
    if /i "!open_folder!"=="y" (
        start "" "dist"
    )
    
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist\
)

echo.
echo 🧹 تنظيف الملفات المؤقتة...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "__pycache__" rmdir /s /q "__pycache__" >nul 2>&1
if exist "*.spec" del "*.spec" >nul 2>&1

echo.
echo 💡 نصائح للاستخدام:
echo    - انسخ الملف التنفيذي إلى أي مكان
echo    - أنشئ اختصار على سطح المكتب
echo    - شارك الملف مع الآخرين
echo    - لا يحتاج Python على الأجهزة الأخرى

echo.
echo 🙏 شكراً لاستخدام أداة البناء البسيطة!
pause
