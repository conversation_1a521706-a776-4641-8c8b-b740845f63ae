#!/usr/bin/env python3
"""
إنشاء شعار لأداة تحميل YouTube
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_youtube_downloader_logo():
    """إنشاء شعار أداة تحميل YouTube"""
    
    # أبعاد الشعار
    width, height = 64, 64
    
    # إنشاء صورة جديدة بخلفية شفافة
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان YouTube
    youtube_red = (255, 0, 0)
    white = (255, 255, 255)
    dark_gray = (33, 33, 33)
    
    # رسم خلفية دائرية حمراء
    circle_margin = 4
    draw.ellipse([circle_margin, circle_margin, width-circle_margin, height-circle_margin], 
                 fill=youtube_red, outline=dark_gray, width=2)
    
    # رسم مثلث التشغيل (أيقونة YouTube)
    triangle_size = 16
    center_x, center_y = width // 2, height // 2
    
    # نقاط المثلث
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    draw.polygon(triangle_points, fill=white)
    
    # رسم سهم تحميل صغير في الزاوية
    arrow_size = 12
    arrow_x = width - arrow_size - 4
    arrow_y = height - arrow_size - 4
    
    # رسم سهم للأسفل
    arrow_points = [
        (arrow_x + arrow_size//2, arrow_y),
        (arrow_x, arrow_y + arrow_size//2),
        (arrow_x + arrow_size//4, arrow_y + arrow_size//2),
        (arrow_x + arrow_size//4, arrow_y + arrow_size),
        (arrow_x + 3*arrow_size//4, arrow_y + arrow_size),
        (arrow_x + 3*arrow_size//4, arrow_y + arrow_size//2),
        (arrow_x + arrow_size, arrow_y + arrow_size//2)
    ]
    
    draw.polygon(arrow_points, fill=white, outline=dark_gray, width=1)
    
    # حفظ الشعار
    img.save('youtube_downloader_icon.png', 'PNG')
    print("✅ تم إنشاء الشعار: youtube_downloader_icon.png")
    
    return 'youtube_downloader_icon.png'

def create_banner_logo():
    """إنشاء شعار أفقي للواجهة"""
    
    # أبعاد البانر
    width, height = 400, 80
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (width, height), (240, 248, 255, 255))  # خلفية زرقاء فاتحة
    draw = ImageDraw.Draw(img)
    
    # ألوان
    youtube_red = (255, 0, 0)
    white = (255, 255, 255)
    dark_blue = (46, 134, 171)
    black = (0, 0, 0)
    
    # رسم خلفية متدرجة
    for i in range(height):
        alpha = int(255 * (1 - i / height * 0.3))
        color = (240, 248, 255, alpha)
        draw.line([(0, i), (width, i)], fill=color)
    
    # رسم أيقونة YouTube في الجانب الأيسر
    icon_size = 50
    icon_x = 20
    icon_y = (height - icon_size) // 2
    
    # دائرة حمراء
    draw.ellipse([icon_x, icon_y, icon_x + icon_size, icon_y + icon_size], 
                 fill=youtube_red, outline=(200, 0, 0), width=2)
    
    # مثلث التشغيل
    triangle_size = 20
    center_x = icon_x + icon_size // 2
    center_y = icon_y + icon_size // 2
    
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    draw.polygon(triangle_points, fill=white)
    
    # النص الرئيسي
    try:
        # محاولة استخدام خط عربي
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_small = ImageFont.truetype("arial.ttf", 14)
    except:
        # استخدام الخط الافتراضي
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # النص الإنجليزي
    text_main = "YouTube Video Downloader"
    text_x = icon_x + icon_size + 20
    text_y = icon_y + 5
    
    draw.text((text_x, text_y), text_main, fill=dark_blue, font=font_large)
    
    # النص العربي
    text_arabic = "أداة تحميل فيديوهات يوتيوب"
    draw.text((text_x, text_y + 30), text_arabic, fill=(100, 100, 100), font=font_small)
    
    # رسم سهم تحميل في الجانب الأيمن
    arrow_size = 30
    arrow_x = width - arrow_size - 20
    arrow_y = (height - arrow_size) // 2
    
    # سهم للأسفل مع دائرة
    draw.ellipse([arrow_x, arrow_y, arrow_x + arrow_size, arrow_y + arrow_size], 
                 fill=(46, 134, 171), outline=(30, 100, 140), width=2)
    
    # سهم أبيض
    arrow_points = [
        (arrow_x + arrow_size//2, arrow_y + 8),
        (arrow_x + 8, arrow_y + arrow_size//2),
        (arrow_x + 12, arrow_y + arrow_size//2),
        (arrow_x + 12, arrow_y + arrow_size - 8),
        (arrow_x + arrow_size - 12, arrow_y + arrow_size - 8),
        (arrow_x + arrow_size - 12, arrow_y + arrow_size//2),
        (arrow_x + arrow_size - 8, arrow_y + arrow_size//2)
    ]
    
    draw.polygon(arrow_points, fill=white)
    
    # حفظ البانر
    img.save('youtube_downloader_banner.png', 'PNG')
    print("✅ تم إنشاء البانر: youtube_downloader_banner.png")
    
    return 'youtube_downloader_banner.png'

def create_simple_icon():
    """إنشاء أيقونة بسيطة للنوافذ"""
    
    # أبعاد الأيقونة
    size = 32
    
    # إنشاء صورة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان
    youtube_red = (255, 0, 0)
    white = (255, 255, 255)
    
    # رسم مربع أحمر
    margin = 2
    draw.rectangle([margin, margin, size-margin, size-margin], 
                   fill=youtube_red, outline=(200, 0, 0), width=1)
    
    # رسم مثلث التشغيل
    triangle_size = 10
    center_x, center_y = size // 2, size // 2
    
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    draw.polygon(triangle_points, fill=white)
    
    # رسم سهم صغير
    arrow_x = size - 8
    arrow_y = size - 8
    
    draw.polygon([
        (arrow_x, arrow_y - 4),
        (arrow_x - 2, arrow_y - 2),
        (arrow_x - 1, arrow_y - 2),
        (arrow_x - 1, arrow_y),
        (arrow_x + 1, arrow_y),
        (arrow_x + 1, arrow_y - 2),
        (arrow_x + 2, arrow_y - 2)
    ], fill=white)
    
    # حفظ الأيقونة
    img.save('youtube_icon_small.png', 'PNG')
    print("✅ تم إنشاء الأيقونة الصغيرة: youtube_icon_small.png")
    
    return 'youtube_icon_small.png'

def main():
    """إنشاء جميع الشعارات"""
    print("🎨 إنشاء شعارات أداة تحميل YouTube...")
    print("=" * 50)
    
    try:
        # التحقق من توفر PIL
        from PIL import Image
        
        # إنشاء الشعارات
        icon_file = create_youtube_downloader_logo()
        banner_file = create_banner_logo()
        small_icon_file = create_simple_icon()
        
        print("\n🎉 تم إنشاء جميع الشعارات بنجاح!")
        print(f"📁 الملفات المنشأة:")
        print(f"   - {icon_file} (أيقونة رئيسية)")
        print(f"   - {banner_file} (بانر للواجهة)")
        print(f"   - {small_icon_file} (أيقونة صغيرة)")
        
        print("\n💡 لاستخدام الشعارات:")
        print("1. ضع الملفات في نفس مجلد البرنامج")
        print("2. أعد تشغيل الواجهة لرؤية الشعار")
        
    except ImportError:
        print("❌ مكتبة PIL غير متوفرة")
        print("لتثبيتها: pip install Pillow")
        
        # إنشاء أيقونة نصية بديلة
        create_text_icon()

def create_text_icon():
    """إنشاء أيقونة نصية بديلة"""
    icon_content = """
    ╔══════════════════════════════════════╗
    ║        🎥 YouTube Downloader         ║
    ║                                      ║
    ║    ▶️  تحميل فيديوهات يوتيوب        ║
    ║                                      ║
    ║         ⬇️ سريع وموثوق              ║
    ╚══════════════════════════════════════╝
    """
    
    with open('youtube_downloader_logo.txt', 'w', encoding='utf-8') as f:
        f.write(icon_content)
    
    print("✅ تم إنشاء شعار نصي: youtube_downloader_logo.txt")

if __name__ == "__main__":
    main()
