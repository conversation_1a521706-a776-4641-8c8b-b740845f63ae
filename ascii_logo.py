#!/usr/bin/env python3
"""
شعار ASCII لأداة تحميل YouTube
"""

from colorama import init, Fore, Style

# تهيئة الألوان
init(autoreset=True)

def print_youtube_logo():
    """طباعة شعار YouTube بـ ASCII"""
    logo = f"""
{Fore.RED}╔══════════════════════════════════════════════════════════════╗
{Fore.RED}║  {Fore.WHITE}▶️  {Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗ {Fore.RED}██████{Fore.WHITE}╗ {Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗{Fore.RED}████████{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗{Fore.RED}██████{Fore.WHITE}╗{Fore.RED}███████{Fore.WHITE}╗  ║
{Fore.RED}║     {Fore.WHITE}╚{Fore.RED}██{Fore.WHITE}╗ {Fore.RED}██{Fore.WHITE}╔╝{Fore.RED}██{Fore.WHITE}╔═══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║╚══{Fore.RED}██{Fore.WHITE}╔══╝{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}╔══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╔════╝  ║
{Fore.RED}║      {Fore.WHITE}╚{Fore.RED}████{Fore.WHITE}╔╝ {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}█████{Fore.WHITE}╗    ║
{Fore.RED}║       {Fore.WHITE}╚{Fore.RED}██{Fore.WHITE}╔╝  {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}╔══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╔══╝    ║
{Fore.RED}║        {Fore.RED}██{Fore.WHITE}║   {Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝{Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝   {Fore.RED}██{Fore.WHITE}║   {Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}███████{Fore.WHITE}╗  ║
{Fore.RED}║        {Fore.WHITE}╚═╝    ╚═════╝  ╚═════╝    ╚═╝    ╚═════╝ ╚═════╝ ╚══════╝  ║
{Fore.RED}╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(logo)

def print_downloader_logo():
    """طباعة شعار أداة التحميل"""
    logo = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
{Fore.CYAN}║                    {Fore.YELLOW}📥 VIDEO DOWNLOADER 📥{Fore.CYAN}                    ║
{Fore.CYAN}║                                                              ║
{Fore.CYAN}║  {Fore.GREEN}⬇️  أداة تحميل فيديوهات يوتيوب السريعة والموثوقة  ⬇️{Fore.CYAN}   ║
{Fore.CYAN}║                                                              ║
{Fore.CYAN}║     {Fore.WHITE}🎥 فيديو    🎵 صوت    📋 قوائم تشغيل    🔧 سهل{Fore.CYAN}      ║
{Fore.CYAN}╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(logo)

def print_simple_logo():
    """طباعة شعار بسيط"""
    logo = f"""
{Fore.RED}    ▶️  {Fore.WHITE}YouTube{Fore.RED} Video Downloader{Style.RESET_ALL}
{Fore.CYAN}    ═══════════════════════════════════{Style.RESET_ALL}
{Fore.GREEN}    🎥 تحميل فيديوهات يوتيوب بسهولة{Style.RESET_ALL}
"""
    print(logo)

def print_mini_logo():
    """طباعة شعار صغير"""
    return f"{Fore.RED}▶️ {Fore.WHITE}YouTube{Fore.RED} Downloader{Style.RESET_ALL}"

def print_status_bar():
    """طباعة شريط الحالة"""
    bar = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
{Fore.CYAN}║  {Fore.GREEN}✅ جاهز للتحميل{Fore.CYAN}  │  {Fore.YELLOW}🔧 سهل الاستخدام{Fore.CYAN}  │  {Fore.BLUE}⚡ سريع وموثوق{Fore.CYAN}  ║
{Fore.CYAN}╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(bar)

def create_logo_variants():
    """إنشاء متغيرات مختلفة من الشعار"""
    
    # شعار كامل
    full_logo = f"""
{Fore.RED}╔══════════════════════════════════════════════════════════════╗
{Fore.RED}║                                                              ║
{Fore.RED}║    {Fore.WHITE}▶️  {Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗ {Fore.RED}██████{Fore.WHITE}╗ {Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗{Fore.RED}████████{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╗   {Fore.RED}██{Fore.WHITE}╗{Fore.RED}██████{Fore.WHITE}╗{Fore.RED}███████{Fore.WHITE}╗    ║
{Fore.RED}║       {Fore.WHITE}╚{Fore.RED}██{Fore.WHITE}╗ {Fore.RED}██{Fore.WHITE}╔╝{Fore.RED}██{Fore.WHITE}╔═══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║╚══{Fore.RED}██{Fore.WHITE}╔══╝{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}╔══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╔════╝    ║
{Fore.RED}║        {Fore.WHITE}╚{Fore.RED}████{Fore.WHITE}╔╝ {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}█████{Fore.WHITE}╗      ║
{Fore.RED}║         {Fore.WHITE}╚{Fore.RED}██{Fore.WHITE}╔╝  {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║   {Fore.RED}██{Fore.WHITE}║{Fore.RED}██{Fore.WHITE}╔══{Fore.RED}██{Fore.WHITE}╗{Fore.RED}██{Fore.WHITE}╔══╝      ║
{Fore.RED}║          {Fore.RED}██{Fore.WHITE}║   {Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝{Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝   {Fore.RED}██{Fore.WHITE}║   {Fore.WHITE}╚{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}██████{Fore.WHITE}╔╝{Fore.RED}███████{Fore.WHITE}╗    ║
{Fore.RED}║          {Fore.WHITE}╚═╝    ╚═════╝  ╚═════╝    ╚═╝    ╚═════╝ ╚═════╝ ╚══════╝    ║
{Fore.RED}║                                                              ║
{Fore.RED}║              {Fore.CYAN}📥 أداة تحميل فيديوهات يوتيوب 📥{Fore.RED}                ║
{Fore.RED}║                                                              ║
{Fore.RED}╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    
    # شعار متوسط
    medium_logo = f"""
{Fore.CYAN}╔════════════════════════════════════════════════╗
{Fore.CYAN}║        {Fore.RED}▶️  YouTube Video Downloader{Fore.CYAN}         ║
{Fore.CYAN}║                                                ║
{Fore.CYAN}║    {Fore.GREEN}🎥 فيديو  🎵 صوت  📋 قوائم  🔧 سهل{Fore.CYAN}     ║
{Fore.CYAN}╚════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    
    # شعار صغير
    small_logo = f"{Fore.RED}▶️ {Fore.WHITE}YouTube{Fore.RED} Downloader {Fore.GREEN}📥{Style.RESET_ALL}"
    
    return {
        'full': full_logo,
        'medium': medium_logo,
        'small': small_logo
    }

def main():
    """عرض جميع الشعارات"""
    print("🎨 شعارات أداة تحميل YouTube")
    print("=" * 50)
    
    print("\n1. الشعار الكامل:")
    print_youtube_logo()
    
    print("\n2. شعار أداة التحميل:")
    print_downloader_logo()
    
    print("\n3. الشعار البسيط:")
    print_simple_logo()
    
    print("\n4. الشعار الصغير:")
    print(print_mini_logo())
    
    print("\n5. شريط الحالة:")
    print_status_bar()
    
    print("\n6. متغيرات الشعار:")
    logos = create_logo_variants()
    
    print("\nالشعار الكامل:")
    print(logos['full'])
    
    print("\nالشعار المتوسط:")
    print(logos['medium'])
    
    print("\nالشعار الصغير:")
    print(logos['small'])

if __name__ == "__main__":
    main()
