#!/usr/bin/env python3
"""
تحديث شعار الاختصار على سطح المكتب
استبدال الشعار القديم بالشعار الجديد المحسّن
"""

import os
import shutil
from pathlib import Path

def update_desktop_shortcut():
    """تحديث اختصار سطح المكتب بالشعار الجديد"""
    
    print("🔄 تحديث اختصار سطح المكتب...")
    
    # مسار سطح المكتب
    desktop_path = Path.home() / "Desktop"
    
    # البحث عن الاختصار الحالي
    shortcut_files = [
        "YouTube Video Downloader.bat",
        "YouTube Video Downloader.lnk"
    ]
    
    found_shortcut = False
    
    for shortcut_file in shortcut_files:
        shortcut_path = desktop_path / shortcut_file
        if shortcut_path.exists():
            print(f"✅ تم العثور على الاختصار: {shortcut_path}")
            found_shortcut = True
            break
    
    if not found_shortcut:
        print("⚠️ لم يتم العثور على اختصار موجود")
        print("سيتم إنشاء اختصار جديد...")
    
    # إنشاء اختصار جديد محسّن
    create_enhanced_shortcut(desktop_path)

def create_enhanced_shortcut(desktop_path):
    """إنشاء اختصار محسّن مع الشعار الجديد"""
    
    # التحقق من وجود الملف التنفيذي
    exe_path = Path("dist/YouTube_Video_Downloader.exe").absolute()
    
    if not exe_path.exists():
        print("❌ الملف التنفيذي غير موجود")
        print("يرجى بناء البرنامج أولاً")
        return False
    
    # محاولة إنشاء اختصار Windows حقيقي
    try:
        import winshell
        from win32com.client import Dispatch
        
        print("🔗 إنشاء اختصار Windows حقيقي...")
        
        # حذف الاختصار القديم
        old_shortcut = desktop_path / "YouTube Video Downloader.lnk"
        if old_shortcut.exists():
            old_shortcut.unlink()
            print("🗑️ تم حذف الاختصار القديم")
        
        # إنشاء اختصار جديد
        shortcut_path = desktop_path / "YouTube Video Downloader.lnk"
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.Description = "أداة تحميل فيديوهات YouTube - محسّنة"
        
        # استخدام الأيقونة الجديدة عالية الجودة
        icon_files = [
            "youtube_downloader_hq.ico",
            "youtube_desktop_icon.png",
            "youtube_downloader_icon_hq.png"
        ]
        
        for icon_file in icon_files:
            if Path(icon_file).exists():
                shortcut.IconLocation = str(Path(icon_file).absolute())
                print(f"🎨 تم تعيين الأيقونة: {icon_file}")
                break
        
        shortcut.save()
        
        print(f"✅ تم إنشاء اختصار Windows حقيقي: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️ مكتبات Windows غير متوفرة")
        print("سيتم إنشاء اختصار batch محسّن...")
        return create_batch_shortcut(desktop_path, exe_path)
    
    except Exception as e:
        print(f"❌ فشل في إنشاء اختصار Windows: {e}")
        print("سيتم إنشاء اختصار batch بديل...")
        return create_batch_shortcut(desktop_path, exe_path)

def create_batch_shortcut(desktop_path, exe_path):
    """إنشاء اختصار batch محسّن"""
    
    # حذف الاختصار القديم
    old_shortcut = desktop_path / "YouTube Video Downloader.bat"
    if old_shortcut.exists():
        old_shortcut.unlink()
        print("🗑️ تم حذف اختصار batch القديم")
    
    # إنشاء اختصار batch جديد محسّن
    shortcut_content = f'''@echo off
title YouTube Video Downloader - محسّن
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎥 YouTube Video Downloader                   ║
echo ║                     أداة التحميل المحسّنة                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 تشغيل أداة تحميل YouTube...
echo.

cd /d "{exe_path.parent}"

if exist "{exe_path.name}" (
    start "" "{exe_path.name}"
    echo ✅ تم تشغيل البرنامج بنجاح!
) else (
    echo ❌ الملف التنفيذي غير موجود
    echo المسار: {exe_path}
    pause
)
'''
    
    shortcut_path = desktop_path / "YouTube Video Downloader.bat"
    
    with open(shortcut_path, 'w', encoding='utf-8') as f:
        f.write(shortcut_content)
    
    print(f"✅ تم إنشاء اختصار batch محسّن: {shortcut_path}")
    return True

def update_exe_icon():
    """تحديث أيقونة الملف التنفيذي"""
    
    print("\n🔄 تحديث أيقونة الملف التنفيذي...")
    
    # التحقق من وجود الأيقونة الجديدة
    new_icon = "youtube_downloader_hq.ico"
    
    if not Path(new_icon).exists():
        print(f"❌ الأيقونة الجديدة غير موجودة: {new_icon}")
        return False
    
    # نسخ الأيقونة الجديدة لتحل محل القديمة
    old_icon = "youtube_downloader.ico"
    
    try:
        shutil.copy2(new_icon, old_icon)
        print(f"✅ تم تحديث الأيقونة: {old_icon}")
        
        # إعادة بناء الملف التنفيذي بالأيقونة الجديدة
        print("\n🔨 إعادة بناء الملف التنفيذي بالأيقونة الجديدة...")
        print("⏳ هذا قد يستغرق بضع دقائق...")
        
        import subprocess
        import sys
        
        # أمر إعادة البناء
        build_command = [
            sys.executable, "-m", "PyInstaller",
            "--onefile", "--windowed",
            "--name", "YouTube_Video_Downloader",
            "--icon", old_icon,
            "--clean",
            "gui_working.py"
        ]
        
        result = subprocess.run(build_command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إعادة بناء الملف التنفيذي بنجاح!")
            
            # التحقق من الملف الجديد
            new_exe = Path("dist/YouTube_Video_Downloader.exe")
            if new_exe.exists():
                size_mb = new_exe.stat().st_size / (1024 * 1024)
                print(f"📁 الملف الجديد: {new_exe}")
                print(f"📊 الحجم: {size_mb:.1f} MB")
                return True
            else:
                print("❌ لم يتم العثور على الملف التنفيذي الجديد")
                return False
        else:
            print("❌ فشل في إعادة البناء:")
            print(result.stderr[:500])
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحديث الأيقونة: {e}")
        return False

def create_start_menu_shortcut():
    """إنشاء اختصار محسّن في قائمة ابدأ"""
    
    print("\n📋 تحديث اختصار قائمة ابدأ...")
    
    try:
        # مسار قائمة ابدأ
        start_menu = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs"
        app_folder = start_menu / "YouTube Video Downloader"
        
        # إنشاء مجلد التطبيق
        app_folder.mkdir(exist_ok=True)
        
        # مسار الملف التنفيذي
        exe_path = Path("dist/YouTube_Video_Downloader.exe").absolute()
        
        if not exe_path.exists():
            print("❌ الملف التنفيذي غير موجود")
            return False
        
        # إنشاء اختصار محسّن
        shortcut_content = f'''@echo off
title YouTube Video Downloader
cd /d "{exe_path.parent}"
start "" "{exe_path.name}"
'''
        
        shortcut_path = app_folder / "YouTube Video Downloader.bat"
        
        with open(shortcut_path, 'w', encoding='utf-8') as f:
            f.write(shortcut_content)
        
        # إنشاء اختصار للدليل
        help_content = '''@echo off
title دليل YouTube Video Downloader
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📖 دليل YouTube Video Downloader              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎥 كيفية الاستخدام:
echo    1. الصق رابط فيديو YouTube
echo    2. اختر نوع التحميل (فيديو/صوت)
echo    3. اختر الجودة المطلوبة
echo    4. اضغط Download
echo.
echo 💡 نصائح:
echo    - تأكد من اتصال الإنترنت
echo    - استخدم روابط YouTube صحيحة
echo    - اختر مجلد حفظ مناسب
echo.
echo 🔗 للدعم: github.com/your-repo
echo.
pause
'''
        
        help_path = app_folder / "دليل الاستخدام.bat"
        
        with open(help_path, 'w', encoding='utf-8') as f:
            f.write(help_content)
        
        print(f"✅ تم تحديث قائمة ابدأ: {app_folder}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قائمة ابدأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🎨 تحديث شعار YouTube Video Downloader")
    print("=" * 60)
    
    # التحقق من وجود الشعارات الجديدة
    required_files = [
        "youtube_downloader_hq.ico",
        "youtube_desktop_icon.png",
        "youtube_downloader_icon_hq.png"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات الشعار مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nيرجى تشغيل create_better_logo.py أولاً")
        return
    
    print("✅ جميع ملفات الشعار موجودة")
    
    # تحديث اختصار سطح المكتب
    print("\n" + "="*60)
    update_desktop_shortcut()
    
    # تحديث أيقونة الملف التنفيذي
    print("\n" + "="*60)
    update_exe_icon()
    
    # تحديث قائمة ابدأ
    print("\n" + "="*60)
    create_start_menu_shortcut()
    
    print("\n" + "="*60)
    print("🎉 تم تحديث جميع الشعارات بنجاح!")
    print("\n💡 الآن:")
    print("   - الاختصار على سطح المكتب محدث")
    print("   - الملف التنفيذي له أيقونة جديدة")
    print("   - قائمة ابدأ محدثة")
    print("\n🚀 جرب تشغيل البرنامج من الاختصار الجديد!")

if __name__ == "__main__":
    main()
