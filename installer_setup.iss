; YouTube Video Downloader - Inno Setup Script
; ملف إعداد Inno Setup لإنشاء أداة تثبيت احترافية

[Setup]
; معلومات التطبيق
AppName=YouTube Video Downloader
AppVersion=1.0
AppPublisher=Open Source Community
AppPublisherURL=https://github.com/your-repo
AppSupportURL=https://github.com/your-repo/issues
AppUpdatesURL=https://github.com/your-repo/releases
DefaultDirName={autopf}\YouTube Video Downloader
DefaultGroupName=YouTube Video Downloader
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=README.txt
OutputDir=setup_output
OutputBaseFilename=YouTube_Video_Downloader_Setup
SetupIconFile=youtube_downloader.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=6.1sp1
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; خيارات التثبيت
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "dist\YouTube_Video_Downloader.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\README.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "youtube_downloader_icon.png"; DestDir: "{app}"; Flags: ignoreversion
Source: "youtube_downloader_banner.png"; DestDir: "{app}"; Flags: ignoreversion
Source: "FINAL_SUMMARY.md"; DestDir: "{app}"; DestName: "CHANGELOG.md"; Flags: ignoreversion

[Icons]
Name: "{group}\YouTube Video Downloader"; Filename: "{app}\YouTube_Video_Downloader.exe"
Name: "{group}\{cm:ProgramOnTheWeb,YouTube Video Downloader}"; Filename: "https://github.com/your-repo"
Name: "{group}\{cm:UninstallProgram,YouTube Video Downloader}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\YouTube Video Downloader"; Filename: "{app}\YouTube_Video_Downloader.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\YouTube Video Downloader"; Filename: "{app}\YouTube_Video_Downloader.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\YouTube_Video_Downloader.exe"; Description: "{cm:LaunchProgram,YouTube Video Downloader}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Code]
// كود Pascal للتحكم في عملية التثبيت

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

// التحقق من متطلبات النظام
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 6 then
  begin
    MsgBox('This program requires Windows Vista or later.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // التحقق من المساحة المتاحة
  if GetSpaceOnDisk(ExpandConstant('{app}'), False, False, False) < 100 * 1024 * 1024 then
  begin
    MsgBox('At least 100 MB of free disk space is required.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  Result := True;
end;

// رسائل مخصصة
procedure InitializeWizard();
begin
  WizardForm.WelcomeLabel1.Caption := 'Welcome to YouTube Video Downloader Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install YouTube Video Downloader on your computer.' + #13#10#13#10 +
    'YouTube Video Downloader is a free tool for downloading videos from YouTube with high quality and fast speed.' + #13#10#13#10 +
    'Click Next to continue, or Cancel to exit Setup.';
end;
