@echo off
chcp 65001 >nul
title تشغيل YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 تشغيل YouTube Video Downloader             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 البحث عن الملف التنفيذي...

if exist "dist\YouTube_Video_Downloader.exe" (
    echo ✅ تم العثور على الملف التنفيذي
    echo 📁 المكان: dist\YouTube_Video_Downloader.exe
    
    REM حساب حجم الملف
    for %%A in ("dist\YouTube_Video_Downloader.exe") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo 📊 الحجم: !size_mb! MB
    )
    
    echo.
    echo 🚀 تشغيل البرنامج...
    echo.
    
    start "" "dist\YouTube_Video_Downloader.exe"
    
    echo ✅ تم تشغيل البرنامج!
    echo.
    echo 💡 إذا لم يفتح البرنامج:
    echo    - تأكد من أن Windows محدث
    echo    - شغّل كمدير
    echo    - أضف استثناء في برنامج الحماية
    
) else (
    echo ❌ الملف التنفيذي غير موجود
    echo.
    echo 🔨 هل تريد بناء الملف التنفيذي الآن؟ (y/n)
    set /p build_now="اختر: "
    
    if /i "!build_now!"=="y" (
        echo.
        echo 🏗️ بناء الملف التنفيذي...
        call "بناء_exe_بسيط.bat"
    ) else (
        echo.
        echo 💡 لبناء الملف التنفيذي، شغّل:
        echo    بناء_exe_بسيط.bat
    )
)

echo.
pause
