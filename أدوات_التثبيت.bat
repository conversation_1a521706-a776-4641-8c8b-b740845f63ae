@echo off
chcp 65001 >nul
title أدوات التثبيت - YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🛠️ أدوات التثبيت الشاملة                     ║
echo ║              YouTube Video Downloader                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من الملفات المطلوبة...

if not exist "dist\YouTube_Video_Downloader.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo.
    echo 🔨 هل تريد بناء البرنامج أولاً؟ (y/n)
    set /p build_first="اختر: "
    
    if /i "%build_first%"=="y" (
        echo 🏗️ بناء البرنامج...
        call "بناء_exe_بسيط.bat"
        
        if not exist "dist\YouTube_Video_Downloader.exe" (
            echo ❌ فشل في بناء البرنامج
            pause
            exit /b 1
        )
    ) else (
        echo ❌ لا يمكن المتابعة بدون الملف التنفيذي
        pause
        exit /b 1
    )
)

echo ✅ الملف التنفيذي موجود

REM حساب حجم الملف
for %%A in ("dist\YouTube_Video_Downloader.exe") do (
    set size=%%~zA
    set /a size_mb=!size!/1024/1024
    echo 📊 حجم البرنامج: !size_mb! MB
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎯 اختر أداة التثبيت                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 1. 🚀 تثبيت سريع (Batch)
echo    - أسرع طريقة للتثبيت
echo    - اختصارات تلقائية
echo    - مناسب للاستخدام الشخصي
echo.

echo 2. 🎯 تثبيت مخصص (Batch)
echo    - اختيار مكان التثبيت
echo    - اختيار الاختصارات
echo    - تحكم كامل في الخيارات
echo.

echo 3. 🖥️ أداة تثبيت رسومية (Python)
echo    - واجهة رسومية جميلة
echo    - خيارات متقدمة
echo    - تجربة احترافية
echo.

echo 4. 💻 نسخة محمولة
echo    - لا تحتاج تثبيت
echo    - يمكن نقلها على فلاش ميموري
echo    - مناسبة للاستخدام المؤقت
echo.

echo 5. 🔧 أداة تثبيت Inno Setup (متقدم)
echo    - أداة تثبيت احترافية
echo    - يتطلب Inno Setup مثبت
echo    - للتوزيع التجاري
echo.

echo 6. 🗑️ إلغاء التثبيت
echo    - إزالة البرنامج من النظام
echo    - حذف جميع الملفات والاختصارات
echo.

echo 7. ❌ خروج
echo.

set /p choice="اختر رقم (1-7): "

if "%choice%"=="1" goto QUICK_INSTALL
if "%choice%"=="2" goto CUSTOM_INSTALL
if "%choice%"=="3" goto GUI_INSTALLER
if "%choice%"=="4" goto PORTABLE_INSTALL
if "%choice%"=="5" goto INNO_SETUP
if "%choice%"=="6" goto UNINSTALL
if "%choice%"=="7" goto EXIT
goto MAIN_MENU

:QUICK_INSTALL
echo.
echo 🚀 تشغيل أداة التثبيت السريع...
call "تثبيت_البرنامج.bat"
goto END

:CUSTOM_INSTALL
echo.
echo 🎯 تشغيل أداة التثبيت المخصص...
call "تثبيت_البرنامج.bat"
goto END

:GUI_INSTALLER
echo.
echo 🖥️ تشغيل أداة التثبيت الرسومية...

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 💡 أداة التثبيت الرسومية تتطلب Python
    echo هل تريد استخدام أداة التثبيت البسيطة بدلاً من ذلك؟ (y/n)
    set /p use_simple="اختر: "
    
    if /i "%use_simple%"=="y" (
        goto QUICK_INSTALL
    ) else (
        echo يرجى تثبيت Python أولاً من https://python.org
        pause
        goto MAIN_MENU
    )
)

if exist "installer.py" (
    python installer.py
) else (
    echo ❌ ملف أداة التثبيت الرسومية غير موجود
    echo جاري استخدام أداة التثبيت البسيطة...
    goto QUICK_INSTALL
)
goto END

:PORTABLE_INSTALL
echo.
echo 💻 إنشاء نسخة محمولة...

echo 📁 أدخل مسار النسخة المحمولة:
echo مثال: D:\Programs\YouTube Downloader Portable
set /p "PORTABLE_PATH=المسار: "

if "%PORTABLE_PATH%"=="" (
    echo ❌ يجب إدخال مسار صحيح
    pause
    goto PORTABLE_INSTALL
)

echo.
echo ⏳ جاري إنشاء النسخة المحمولة...

REM إنشاء المجلد
if not exist "%PORTABLE_PATH%" mkdir "%PORTABLE_PATH%"

REM نسخ الملفات
copy "dist\YouTube_Video_Downloader.exe" "%PORTABLE_PATH%\" >nul
if exist "dist\README.txt" copy "dist\README.txt" "%PORTABLE_PATH%\" >nul
if exist "youtube_downloader_icon.png" copy "youtube_downloader_icon.png" "%PORTABLE_PATH%\" >nul
if exist "FINAL_SUMMARY.md" copy "FINAL_SUMMARY.md" "%PORTABLE_PATH%\CHANGELOG.md" >nul

REM إنشاء ملف تشغيل
echo @echo off > "%PORTABLE_PATH%\تشغيل.bat"
echo title YouTube Video Downloader - Portable >> "%PORTABLE_PATH%\تشغيل.bat"
echo cd /d "%%~dp0" >> "%PORTABLE_PATH%\تشغيل.bat"
echo start "" "YouTube_Video_Downloader.exe" >> "%PORTABLE_PATH%\تشغيل.bat"

REM إنشاء README للنسخة المحمولة
echo YouTube Video Downloader - Portable Version > "%PORTABLE_PATH%\README_PORTABLE.txt"
echo ============================================== >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo. >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo هذه نسخة محمولة من YouTube Video Downloader >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo لا تحتاج تثبيت ويمكن تشغيلها من أي مكان >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo. >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo للتشغيل: >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo - انقر مزدوج على "تشغيل.bat" >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo - أو انقر مزدوج على "YouTube_Video_Downloader.exe" >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo. >> "%PORTABLE_PATH%\README_PORTABLE.txt"
echo يمكنك نسخ هذا المجلد إلى فلاش ميموري أو أي مكان آخر >> "%PORTABLE_PATH%\README_PORTABLE.txt"

echo ✅ تم إنشاء النسخة المحمولة بنجاح!
echo 📁 المكان: %PORTABLE_PATH%
echo.

echo هل تريد تشغيل البرنامج الآن؟ (y/n)
set /p run_portable="اختر: "

if /i "%run_portable%"=="y" (
    cd /d "%PORTABLE_PATH%"
    start "" "YouTube_Video_Downloader.exe"
)

goto END

:INNO_SETUP
echo.
echo 🔧 أداة تثبيت Inno Setup...

REM التحقق من وجود Inno Setup
where iscc >nul 2>&1
if errorlevel 1 (
    echo ❌ Inno Setup غير مثبت
    echo.
    echo 💡 لاستخدام هذه الأداة، يجب تثبيت Inno Setup أولاً
    echo يمكنك تحميله من: https://jrsoftware.org/isinfo.php
    echo.
    echo هل تريد استخدام أداة تثبيت أخرى؟ (y/n)
    set /p use_other="اختر: "
    
    if /i "%use_other%"=="y" (
        goto MAIN_MENU
    ) else (
        goto END
    )
)

if exist "installer_setup.iss" (
    echo ⏳ بناء أداة التثبيت الاحترافية...
    iscc "installer_setup.iss"
    
    if exist "setup_output\YouTube_Video_Downloader_Setup.exe" (
        echo ✅ تم إنشاء أداة التثبيت الاحترافية!
        echo 📁 المكان: setup_output\YouTube_Video_Downloader_Setup.exe
        
        echo.
        echo هل تريد تشغيل أداة التثبيت؟ (y/n)
        set /p run_setup="اختر: "
        
        if /i "%run_setup%"=="y" (
            start "" "setup_output\YouTube_Video_Downloader_Setup.exe"
        )
    ) else (
        echo ❌ فشل في إنشاء أداة التثبيت
    )
) else (
    echo ❌ ملف Inno Setup غير موجود
    echo جاري استخدام أداة التثبيت البسيطة...
    goto QUICK_INSTALL
)

goto END

:UNINSTALL
echo.
echo 🗑️ تشغيل أداة إلغاء التثبيت...
call "إلغاء_التثبيت.bat"
goto END

:MAIN_MENU
echo.
echo هل تريد اختيار أداة أخرى؟ (y/n)
set /p try_again="اختر: "

if /i "%try_again%"=="y" (
    cls
    goto :start
)

:EXIT
echo.
echo 👋 وداعاً!
goto END

:END
echo.
echo 🙏 شكراً لاستخدام أدوات التثبيت!
pause

:start
