#!/usr/bin/env python3
"""
Advanced Features for YouTube Downloader
Additional utilities for subtitle download, format conversion, and batch processing
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Optional
from downloader import YouTubeDownloader
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)


class AdvancedDownloader(YouTubeDownloader):
    """Extended downloader with advanced features"""
    
    def __init__(self, download_path: str = "downloads"):
        super().__init__(download_path)
        self.config_file = self.download_path / "downloader_config.json"
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        self.config = {
            'default_quality': 'highest',
            'default_audio_format': 'mp3',
            'auto_subtitle_download': False,
            'preferred_subtitle_languages': ['en'],
            'auto_convert_format': None,
            'max_concurrent_downloads': 3
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
            except Exception as e:
                print(f"Warning: Could not load config: {e}")
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save config: {e}")
    
    def download_with_subtitles(self, url: str, quality: str = None, 
                               subtitle_languages: List[str] = None) -> Dict[str, str]:
        """
        Download video with subtitles
        
        Args:
            url: YouTube video URL
            quality: Video quality
            subtitle_languages: List of subtitle languages to download
            
        Returns:
            Dictionary with video and subtitle file paths
        """
        quality = quality or self.config['default_quality']
        subtitle_languages = subtitle_languages or self.config['preferred_subtitle_languages']
        
        result = {'video': None, 'subtitles': []}
        
        try:
            # Download video
            print(f"{Fore.GREEN}📹 Downloading video...{Style.RESET_ALL}")
            video_path = self.download_video(url, quality)
            result['video'] = video_path
            
            # Download subtitles if available
            if self.config['auto_subtitle_download'] or subtitle_languages:
                print(f"{Fore.YELLOW}📝 Downloading subtitles...{Style.RESET_ALL}")
                try:
                    subtitle_paths = self.download_subtitles(url, subtitle_languages)
                    result['subtitles'] = subtitle_paths
                    print(f"{Fore.GREEN}✅ Downloaded {len(subtitle_paths)} subtitle files{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.YELLOW}⚠️  Could not download subtitles: {e}{Style.RESET_ALL}")
            
            return result
            
        except Exception as e:
            raise Exception(f"Error in advanced download: {str(e)}")
    
    def batch_download_with_options(self, urls: List[str], options: Dict) -> List[Dict]:
        """
        Advanced batch download with various options
        
        Args:
            urls: List of YouTube URLs
            options: Download options dictionary
            
        Returns:
            List of download results
        """
        results = []
        
        download_type = options.get('type', 'video')
        quality = options.get('quality', self.config['default_quality'])
        audio_format = options.get('audio_format', self.config['default_audio_format'])
        include_subtitles = options.get('subtitles', False)
        subtitle_languages = options.get('subtitle_languages', ['en'])
        convert_format = options.get('convert_format', None)
        
        for i, url in enumerate(urls):
            print(f"\n{Fore.CYAN}📥 Processing {i+1}/{len(urls)}: {url}{Style.RESET_ALL}")
            
            try:
                result = {'url': url, 'files': [], 'error': None}
                
                if download_type == 'video':
                    if include_subtitles:
                        download_result = self.download_with_subtitles(url, quality, subtitle_languages)
                        result['files'].append(download_result['video'])
                        result['files'].extend(download_result['subtitles'])
                    else:
                        video_path = self.download_video(url, quality)
                        result['files'].append(video_path)
                    
                    # Convert format if requested
                    if convert_format and result['files']:
                        video_file = result['files'][0]  # First file should be video
                        converted_path = self.convert_video_format(video_file, convert_format)
                        result['files'].append(converted_path)
                        print(f"{Fore.GREEN}🔄 Converted to {convert_format}: {converted_path}{Style.RESET_ALL}")
                
                elif download_type == 'audio':
                    audio_path = self.download_audio(url, audio_format)
                    result['files'].append(audio_path)
                
                results.append(result)
                print(f"{Fore.GREEN}✅ Completed: {len(result['files'])} files{Style.RESET_ALL}")
                
            except Exception as e:
                error_msg = str(e)
                print(f"{Fore.RED}❌ Error: {error_msg}{Style.RESET_ALL}")
                results.append({'url': url, 'files': [], 'error': error_msg})
        
        return results
    
    def create_download_report(self, results: List[Dict], output_file: str = None) -> str:
        """
        Create a detailed download report
        
        Args:
            results: List of download results
            output_file: Optional output file path
            
        Returns:
            Report content as string
        """
        report_lines = []
        report_lines.append("YouTube Download Report")
        report_lines.append("=" * 50)
        report_lines.append(f"Total URLs processed: {len(results)}")
        
        successful = [r for r in results if not r.get('error')]
        failed = [r for r in results if r.get('error')]
        
        report_lines.append(f"Successful downloads: {len(successful)}")
        report_lines.append(f"Failed downloads: {len(failed)}")
        report_lines.append("")
        
        # Successful downloads
        if successful:
            report_lines.append("SUCCESSFUL DOWNLOADS:")
            report_lines.append("-" * 30)
            for result in successful:
                report_lines.append(f"URL: {result['url']}")
                report_lines.append(f"Files downloaded: {len(result['files'])}")
                for file_path in result['files']:
                    report_lines.append(f"  - {file_path}")
                report_lines.append("")
        
        # Failed downloads
        if failed:
            report_lines.append("FAILED DOWNLOADS:")
            report_lines.append("-" * 30)
            for result in failed:
                report_lines.append(f"URL: {result['url']}")
                report_lines.append(f"Error: {result['error']}")
                report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        # Save to file if specified
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"{Fore.GREEN}📄 Report saved to: {output_file}{Style.RESET_ALL}")
        
        return report_content
    
    def interactive_config(self):
        """Interactive configuration setup"""
        print(f"\n{Fore.CYAN}⚙️  Configuration Setup{Style.RESET_ALL}")
        print("Current settings:")
        
        for key, value in self.config.items():
            print(f"  {key}: {value}")
        
        print(f"\n{Fore.YELLOW}Would you like to modify any settings? (y/n): {Style.RESET_ALL}", end="")
        if input().lower() != 'y':
            return
        
        # Default quality
        print(f"\n{Fore.CYAN}Default video quality{Style.RESET_ALL} (current: {self.config['default_quality']}):")
        print("Options: highest, lowest, 720p, 1080p, 480p, 360p")
        new_quality = input("Enter new value (or press Enter to keep current): ").strip()
        if new_quality:
            self.config['default_quality'] = new_quality
        
        # Default audio format
        print(f"\n{Fore.CYAN}Default audio format{Style.RESET_ALL} (current: {self.config['default_audio_format']}):")
        print("Options: mp3, wav, webm")
        new_format = input("Enter new value (or press Enter to keep current): ").strip()
        if new_format:
            self.config['default_audio_format'] = new_format
        
        # Auto subtitle download
        print(f"\n{Fore.CYAN}Auto download subtitles{Style.RESET_ALL} (current: {self.config['auto_subtitle_download']}):")
        auto_sub = input("Enable auto subtitle download? (y/n): ").strip().lower()
        if auto_sub in ['y', 'n']:
            self.config['auto_subtitle_download'] = auto_sub == 'y'
        
        # Preferred subtitle languages
        print(f"\n{Fore.CYAN}Preferred subtitle languages{Style.RESET_ALL} (current: {self.config['preferred_subtitle_languages']}):")
        print("Enter language codes separated by commas (e.g., en,es,fr)")
        new_langs = input("Enter new value (or press Enter to keep current): ").strip()
        if new_langs:
            self.config['preferred_subtitle_languages'] = [lang.strip() for lang in new_langs.split(',')]
        
        # Save configuration
        self.save_config()
        print(f"\n{Fore.GREEN}✅ Configuration saved!{Style.RESET_ALL}")
    
    def cleanup_temp_files(self):
        """Clean up temporary files in download directory"""
        temp_extensions = ['.part', '.tmp', '.temp', '.webm.part']
        cleaned_files = []
        
        for file_path in self.download_path.rglob('*'):
            if file_path.is_file():
                if any(str(file_path).endswith(ext) for ext in temp_extensions):
                    try:
                        file_path.unlink()
                        cleaned_files.append(str(file_path))
                    except Exception as e:
                        print(f"Could not delete {file_path}: {e}")
        
        if cleaned_files:
            print(f"{Fore.GREEN}🧹 Cleaned up {len(cleaned_files)} temporary files{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}No temporary files found{Style.RESET_ALL}")
        
        return cleaned_files


def main():
    """Main function for advanced features demo"""
    print(f"{Fore.CYAN}YouTube Downloader - Advanced Features{Style.RESET_ALL}")
    
    downloader = AdvancedDownloader()
    
    while True:
        print(f"\n{Fore.CYAN}Advanced Options:{Style.RESET_ALL}")
        print("1. Download with subtitles")
        print("2. Batch download with options")
        print("3. Configure settings")
        print("4. Clean up temporary files")
        print("5. Exit")
        
        choice = input(f"\n{Fore.YELLOW}Enter your choice (1-5): {Style.RESET_ALL}").strip()
        
        try:
            if choice == '1':
                url = input("Enter YouTube URL: ").strip()
                if url:
                    result = downloader.download_with_subtitles(url)
                    print(f"Downloaded: {result}")
            
            elif choice == '2':
                file_path = input("Enter path to file with URLs: ").strip()
                if Path(file_path).exists():
                    with open(file_path, 'r') as f:
                        urls = [line.strip() for line in f if line.strip()]
                    
                    options = {
                        'type': 'video',
                        'quality': 'highest',
                        'subtitles': True,
                        'subtitle_languages': ['en']
                    }
                    
                    results = downloader.batch_download_with_options(urls, options)
                    report = downloader.create_download_report(results, 'download_report.txt')
                    print(report)
            
            elif choice == '3':
                downloader.interactive_config()
            
            elif choice == '4':
                downloader.cleanup_temp_files()
            
            elif choice == '5':
                break
            
            else:
                print(f"{Fore.RED}Invalid choice{Style.RESET_ALL}")
                
        except Exception as e:
            print(f"{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")


if __name__ == "__main__":
    main()
