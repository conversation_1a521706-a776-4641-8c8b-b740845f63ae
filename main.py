#!/usr/bin/env python3
"""
YouTube Video Downloader - Command Line Interface
A comprehensive tool for downloading YouTube videos, audio, and playlists
"""

import os
import sys
import argparse
from pathlib import Path
from colorama import init, Fore, Style
from downloader import YouTubeDownloader, progress_function

# Initialize colorama for cross-platform colored output
init(autoreset=True)


def print_banner():
    """Print application banner"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    YouTube Video Downloader                  ║
║                     Command Line Interface                   ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


def print_video_info(info):
    """Print formatted video information"""
    print(f"\n{Fore.GREEN}📹 Video Information:{Style.RESET_ALL}")
    print(f"   Title: {info['title']}")
    print(f"   Author: {info['author']}")
    print(f"   Duration: {info['length']} seconds")
    print(f"   Views: {info['views']:,}")
    print(f"   Video ID: {info['video_id']}")


def print_streams_info(streams):
    """Print available streams information"""
    print(f"\n{Fore.YELLOW}📊 Available Streams:{Style.RESET_ALL}")
    
    if streams['video_streams']:
        print(f"\n{Fore.BLUE}🎥 Video Streams:{Style.RESET_ALL}")
        for i, stream in enumerate(streams['video_streams'], 1):
            size_mb = stream['filesize'] / (1024 * 1024) if stream['filesize'] else 0
            print(f"   {i}. Resolution: {stream['resolution']}, "
                  f"FPS: {stream['fps']}, Size: {size_mb:.1f} MB")
    
    if streams['audio_streams']:
        print(f"\n{Fore.MAGENTA}🎵 Audio Streams:{Style.RESET_ALL}")
        for i, stream in enumerate(streams['audio_streams'], 1):
            size_mb = stream['filesize'] / (1024 * 1024) if stream['filesize'] else 0
            print(f"   {i}. Bitrate: {stream['abr']}, Size: {size_mb:.1f} MB")


def interactive_mode():
    """Run the downloader in interactive mode"""
    print_banner()
    
    downloader = YouTubeDownloader()
    
    while True:
        print(f"\n{Fore.CYAN}🔧 Choose an option:{Style.RESET_ALL}")
        print("1. Download single video")
        print("2. Download audio only")
        print("3. Download playlist")
        print("4. Download from file")
        print("5. Get video information")
        print("6. Exit")
        
        choice = input(f"\n{Fore.YELLOW}Enter your choice (1-6): {Style.RESET_ALL}").strip()
        
        try:
            if choice == '1':
                download_single_video(downloader)
            elif choice == '2':
                download_audio_only(downloader)
            elif choice == '3':
                download_playlist(downloader)
            elif choice == '4':
                download_from_file(downloader)
            elif choice == '5':
                get_video_info(downloader)
            elif choice == '6':
                print(f"\n{Fore.GREEN}👋 Thank you for using YouTube Downloader!{Style.RESET_ALL}")
                break
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please try again.{Style.RESET_ALL}")
                
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}⚠️  Operation cancelled by user.{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}❌ Error: {str(e)}{Style.RESET_ALL}")


def download_single_video(downloader):
    """Download a single video"""
    url = input(f"\n{Fore.CYAN}🔗 Enter YouTube video URL: {Style.RESET_ALL}").strip()
    
    if not url:
        print(f"{Fore.RED}❌ URL cannot be empty.{Style.RESET_ALL}")
        return
    
    # Get video info first
    try:
        info = downloader.get_video_info(url)
        print_video_info(info)
        
        # Get available streams
        streams = downloader.get_available_streams(url)
        print_streams_info(streams)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error getting video info: {str(e)}{Style.RESET_ALL}")
        return
    
    # Quality selection
    print(f"\n{Fore.CYAN}📺 Quality options:{Style.RESET_ALL}")
    print("1. Highest available")
    print("2. Lowest available")
    print("3. Specific resolution (e.g., 720p, 1080p)")
    
    quality_choice = input(f"\n{Fore.YELLOW}Choose quality option (1-3): {Style.RESET_ALL}").strip()
    
    if quality_choice == '1':
        quality = 'highest'
    elif quality_choice == '2':
        quality = 'lowest'
    elif quality_choice == '3':
        quality = input(f"{Fore.CYAN}Enter resolution (e.g., 720p): {Style.RESET_ALL}").strip()
    else:
        quality = 'highest'
        print(f"{Fore.YELLOW}⚠️  Using highest quality as default.{Style.RESET_ALL}")
    
    # Download
    print(f"\n{Fore.GREEN}⬇️  Starting download...{Style.RESET_ALL}")
    try:
        filepath = downloader.download_video(url, quality, progress_function)
        print(f"\n{Fore.GREEN}✅ Video downloaded successfully!{Style.RESET_ALL}")
        print(f"   📁 Saved to: {filepath}")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Download failed: {str(e)}{Style.RESET_ALL}")


def download_audio_only(downloader):
    """Download audio only"""
    url = input(f"\n{Fore.CYAN}🔗 Enter YouTube video URL: {Style.RESET_ALL}").strip()
    
    if not url:
        print(f"{Fore.RED}❌ URL cannot be empty.{Style.RESET_ALL}")
        return
    
    # Format selection
    print(f"\n{Fore.CYAN}🎵 Audio format:{Style.RESET_ALL}")
    print("1. MP3")
    print("2. Original format (WebM)")
    
    format_choice = input(f"\n{Fore.YELLOW}Choose format (1-2): {Style.RESET_ALL}").strip()
    
    if format_choice == '1':
        audio_format = 'mp3'
    else:
        audio_format = 'webm'
    
    # Download
    print(f"\n{Fore.GREEN}⬇️  Starting audio download...{Style.RESET_ALL}")
    try:
        filepath = downloader.download_audio(url, audio_format, progress_function)
        print(f"\n{Fore.GREEN}✅ Audio downloaded successfully!{Style.RESET_ALL}")
        print(f"   📁 Saved to: {filepath}")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Download failed: {str(e)}{Style.RESET_ALL}")


def download_playlist(downloader):
    """Download playlist"""
    url = input(f"\n{Fore.CYAN}🔗 Enter YouTube playlist URL: {Style.RESET_ALL}").strip()
    
    if not url:
        print(f"{Fore.RED}❌ URL cannot be empty.{Style.RESET_ALL}")
        return
    
    # Download type
    print(f"\n{Fore.CYAN}📋 Download type:{Style.RESET_ALL}")
    print("1. Videos")
    print("2. Audio only")
    
    type_choice = input(f"\n{Fore.YELLOW}Choose type (1-2): {Style.RESET_ALL}").strip()
    download_type = 'video' if type_choice == '1' else 'audio'
    
    # Max videos
    max_videos_input = input(f"\n{Fore.CYAN}📊 Maximum videos to download (press Enter for all): {Style.RESET_ALL}").strip()
    max_videos = int(max_videos_input) if max_videos_input.isdigit() else None
    
    # Quality for videos
    quality = 'highest'
    if download_type == 'video':
        quality_input = input(f"\n{Fore.CYAN}📺 Video quality (highest/lowest/720p/1080p): {Style.RESET_ALL}").strip()
        quality = quality_input if quality_input else 'highest'
    
    # Download
    print(f"\n{Fore.GREEN}⬇️  Starting playlist download...{Style.RESET_ALL}")
    try:
        filepaths = downloader.download_playlist(url, download_type, quality, max_videos)
        print(f"\n{Fore.GREEN}✅ Playlist downloaded successfully!{Style.RESET_ALL}")
        print(f"   📊 Downloaded {len(filepaths)} files")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Download failed: {str(e)}{Style.RESET_ALL}")


def download_from_file(downloader):
    """Download from file containing URLs"""
    file_path = input(f"\n{Fore.CYAN}📄 Enter path to file with URLs: {Style.RESET_ALL}").strip()
    
    if not file_path or not Path(file_path).exists():
        print(f"{Fore.RED}❌ File not found.{Style.RESET_ALL}")
        return
    
    # Download type
    print(f"\n{Fore.CYAN}📋 Download type:{Style.RESET_ALL}")
    print("1. Videos")
    print("2. Audio only")
    
    type_choice = input(f"\n{Fore.YELLOW}Choose type (1-2): {Style.RESET_ALL}").strip()
    download_type = 'video' if type_choice == '1' else 'audio'
    
    # Quality for videos
    quality = 'highest'
    if download_type == 'video':
        quality_input = input(f"\n{Fore.CYAN}📺 Video quality (highest/lowest/720p/1080p): {Style.RESET_ALL}").strip()
        quality = quality_input if quality_input else 'highest'
    
    # Download
    print(f"\n{Fore.GREEN}⬇️  Starting batch download...{Style.RESET_ALL}")
    try:
        filepaths = downloader.download_from_file(file_path, download_type, quality)
        print(f"\n{Fore.GREEN}✅ Batch download completed!{Style.RESET_ALL}")
        print(f"   📊 Downloaded {len(filepaths)} files")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Download failed: {str(e)}{Style.RESET_ALL}")


def get_video_info(downloader):
    """Get and display video information"""
    url = input(f"\n{Fore.CYAN}🔗 Enter YouTube video URL: {Style.RESET_ALL}").strip()
    
    if not url:
        print(f"{Fore.RED}❌ URL cannot be empty.{Style.RESET_ALL}")
        return
    
    try:
        info = downloader.get_video_info(url)
        print_video_info(info)
        
        streams = downloader.get_available_streams(url)
        print_streams_info(streams)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error getting video info: {str(e)}{Style.RESET_ALL}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="YouTube Video Downloader - Download videos, audio, and playlists from YouTube",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Interactive mode
  python main.py -u "VIDEO_URL"                     # Download video
  python main.py -u "VIDEO_URL" -a                  # Download audio only
  python main.py -u "PLAYLIST_URL" -p               # Download playlist
  python main.py -f urls.txt                        # Download from file
  python main.py -u "VIDEO_URL" -q 720p             # Specific quality
        """
    )
    
    parser.add_argument('-u', '--url', help='YouTube video or playlist URL')
    parser.add_argument('-a', '--audio', action='store_true', help='Download audio only')
    parser.add_argument('-p', '--playlist', action='store_true', help='Download as playlist')
    parser.add_argument('-q', '--quality', default='highest', help='Video quality (highest, lowest, 720p, 1080p, etc.)')
    parser.add_argument('-f', '--file', help='File containing YouTube URLs (one per line)')
    parser.add_argument('-o', '--output', default='downloads', help='Output directory')
    parser.add_argument('--info', action='store_true', help='Show video information only')
    
    args = parser.parse_args()
    
    # If no arguments provided, run interactive mode
    if len(sys.argv) == 1:
        interactive_mode()
        return
    
    # Create downloader instance
    downloader = YouTubeDownloader(args.output)
    
    try:
        if args.file:
            # Download from file
            download_type = 'audio' if args.audio else 'video'
            filepaths = downloader.download_from_file(args.file, download_type, args.quality)
            print(f"Downloaded {len(filepaths)} files to {args.output}")
            
        elif args.url:
            if args.info:
                # Show info only
                info = downloader.get_video_info(args.url)
                print_video_info(info)
                streams = downloader.get_available_streams(args.url)
                print_streams_info(streams)
                
            elif args.playlist:
                # Download playlist
                download_type = 'audio' if args.audio else 'video'
                filepaths = downloader.download_playlist(args.url, download_type, args.quality)
                print(f"Downloaded {len(filepaths)} files from playlist")
                
            elif args.audio:
                # Download audio
                filepath = downloader.download_audio(args.url, 'mp3', progress_function)
                print(f"\nAudio downloaded: {filepath}")
                
            else:
                # Download video
                filepath = downloader.download_video(args.url, args.quality, progress_function)
                print(f"\nVideo downloaded: {filepath}")
        else:
            parser.print_help()
            
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
