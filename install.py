#!/usr/bin/env python3
"""
Installation and Setup Script for YouTube Downloader
Automatically installs dependencies and sets up the environment
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        # Use list format for subprocess to handle spaces in paths
        if isinstance(command, str):
            if sys.platform == "win32":
                result = subprocess.run(command, shell=True, check=True,
                                      capture_output=True, text=True)
            else:
                result = subprocess.run(command.split(), check=True,
                                      capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=True,
                                  capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    # Upgrade pip first
    pip_upgrade_cmd = [sys.executable, "-m", "pip", "install", "--upgrade", "pip"]
    if not run_command(pip_upgrade_cmd, "Upgrading pip"):
        return False

    # Install requirements
    pip_install_cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    if not run_command(pip_install_cmd, "Installing Python packages"):
        return False
    
    return True


def create_directories():
    """Create necessary directories"""
    directories = ["downloads", "downloads/videos", "downloads/audio", "downloads/subtitles"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    return True


def test_installation():
    """Test if the installation works"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test importing main modules
        from downloader import YouTubeDownloader
        print("✅ Core downloader module imported successfully")
        
        # Test creating downloader instance
        downloader = YouTubeDownloader("downloads")
        print("✅ Downloader instance created successfully")
        
        # Test GUI imports (optional)
        try:
            import tkinter as tk
            print("✅ GUI dependencies available")
        except ImportError:
            print("⚠️  GUI dependencies not available (tkinter not found)")
            print("   GUI functionality will not work, but CLI will work fine")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False


def show_usage_examples():
    """Show usage examples"""
    print("\n📖 Usage Examples:")
    print("=" * 50)
    
    examples = [
        ("Interactive mode (recommended for beginners)", "python main.py"),
        ("Download a video", 'python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID"'),
        ("Download audio only", 'python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID" -a'),
        ("Launch GUI", "python gui.py"),
        ("Advanced features", "python advanced_features.py"),
        ("Get help", "python main.py --help")
    ]
    
    for description, command in examples:
        print(f"\n{description}:")
        print(f"  {command}")


def main():
    """Main installation function"""
    print("🚀 YouTube Downloader Installation Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Installation failed during package installation")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("\n❌ Installation failed during directory creation")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed")
        print("Some components may not work correctly")
    
    print("\n🎉 Installation completed successfully!")
    print("\nYour YouTube Downloader is ready to use!")
    
    # Show usage examples
    show_usage_examples()
    
    print("\n📋 Next Steps:")
    print("1. Try the interactive mode: python main.py")
    print("2. Or launch the GUI: python gui.py")
    print("3. Read README.md for detailed documentation")
    
    print("\n⚠️  Important Reminders:")
    print("- Only download videos you have permission to download")
    print("- Respect copyright laws and YouTube's Terms of Service")
    print("- Ensure stable internet connection for downloads")


if __name__ == "__main__":
    main()
