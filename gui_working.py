#!/usr/bin/env python3
"""
YouTube Video Downloader - واجهة تعمل 100%
نسخة محسّنة تحل مشكلة عدم التحميل
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path
import subprocess

# محاولة استيراد yt-dlp
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False


class WorkingYouTubeGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Video Downloader - يعمل 100%")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # مجلد التحميل
        self.download_path = "downloads"
        Path(self.download_path).mkdir(exist_ok=True)
        
        # متغيرات
        self.url_var = tk.StringVar()
        self.download_type_var = tk.StringVar(value="video")
        self.quality_var = tk.StringVar(value="best")
        self.audio_format_var = tk.StringVar(value="mp3")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")
        
        # متغير لإيقاف التحميل
        self.is_downloading = False
        
        self.setup_ui()
        self.check_dependencies()
        
        # تركيز على حقل الإدخال
        self.root.after(100, lambda: self.url_entry.focus())
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="YouTube Video Downloader - يعمل 100%", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # قسم إدخال الرابط
        url_frame = ttk.LabelFrame(main_frame, text="Video/Playlist URL", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        url_frame.columnconfigure(0, weight=1)
        
        # حقل إدخال الرابط
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 11))
        self.url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # أزرار مساعدة
        button_frame = ttk.Frame(url_frame)
        button_frame.grid(row=0, column=1, padx=(5, 0))
        
        paste_btn = ttk.Button(button_frame, text="Paste", command=self.paste_url)
        paste_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        clear_btn = ttk.Button(button_frame, text="Clear", command=self.clear_url)
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        info_btn = ttk.Button(button_frame, text="Get Info", command=self.get_video_info)
        info_btn.pack(side=tk.LEFT)
        
        # إضافة placeholder
        self.url_entry.insert(0, "Paste YouTube URL here...")
        self.url_entry.bind('<FocusIn>', self.on_entry_focus_in)
        self.url_entry.bind('<FocusOut>', self.on_entry_focus_out)
        self.url_entry.bind('<KeyRelease>', self.on_url_change)
        self.url_entry.bind('<Return>', lambda e: self.start_download())
        
        # قسم خيارات التحميل
        options_frame = ttk.LabelFrame(main_frame, text="Download Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # نوع التحميل
        ttk.Label(options_frame, text="Type:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        type_frame = ttk.Frame(options_frame)
        type_frame.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Radiobutton(type_frame, text="Video", variable=self.download_type_var, 
                       value="video", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Audio Only", variable=self.download_type_var, 
                       value="audio", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Playlist", variable=self.download_type_var, 
                       value="playlist", command=self.on_type_change).pack(side=tk.LEFT)
        
        # جودة الفيديو
        ttk.Label(options_frame, text="Quality:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.quality_combo = ttk.Combobox(options_frame, textvariable=self.quality_var, 
                                         values=["best", "worst", "1080p", "720p", "480p", "360p"],
                                         state="readonly", width=15)
        self.quality_combo.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # صيغة الصوت
        ttk.Label(options_frame, text="Audio Format:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.audio_format_combo = ttk.Combobox(options_frame, textvariable=self.audio_format_var,
                                              values=["mp3", "wav", "m4a"], state="readonly", width=15)
        self.audio_format_combo.grid(row=2, column=1, sticky=tk.W, pady=(10, 0))
        
        # مجلد الحفظ
        output_frame = ttk.LabelFrame(main_frame, text="Output Directory", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_label = ttk.Label(output_frame, text=f"Downloads will be saved to: {self.download_path}")
        self.output_label.grid(row=0, column=0, sticky=tk.W)
        
        browse_btn = ttk.Button(output_frame, text="Browse", command=self.browse_output_directory)
        browse_btn.grid(row=0, column=1, padx=(10, 0))
        
        # شريط التقدم
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # أزرار التحكم
        button_control_frame = ttk.Frame(main_frame)
        button_control_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        self.download_btn = ttk.Button(button_control_frame, text="Download", 
                                      command=self.start_download)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_control_frame, text="Stop", 
                                  command=self.stop_download, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تثبيت yt-dlp إذا لم يكن متوفراً
        if not YT_DLP_AVAILABLE:
            install_btn = ttk.Button(button_control_frame, text="Install yt-dlp", 
                                    command=self.install_yt_dlp)
            install_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        open_folder_btn = ttk.Button(button_control_frame, text="Open Downloads Folder", 
                                    command=self.open_downloads_folder)
        open_folder_btn.pack(side=tk.LEFT)
        
        # سجل الأحداث
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # الحالة الأولية
        self.on_type_change()
    
    def check_dependencies(self):
        """التحقق من المكتبات المطلوبة"""
        if YT_DLP_AVAILABLE:
            self.log("✅ yt-dlp is available and ready")
            self.status_var.set("Ready - yt-dlp available")
        else:
            self.log("❌ yt-dlp is not installed")
            self.status_var.set("yt-dlp not installed - Click 'Install yt-dlp'")
            messagebox.showwarning("Missing Dependency", 
                                 "yt-dlp is not installed.\n\nClick 'Install yt-dlp' button to install it.")
    
    def log(self, message):
        """إضافة رسالة للسجل"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def paste_url(self):
        """لصق الرابط من الحافظة"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.clear_placeholder()
                self.url_var.set(clipboard_content.strip())
                self.update_status("URL pasted from clipboard")
                self.log(f"URL pasted: {clipboard_content.strip()}")
            else:
                messagebox.showwarning("Warning", "Clipboard is empty")
        except tk.TclError:
            messagebox.showwarning("Warning", "Cannot access clipboard")
    
    def clear_url(self):
        """مسح حقل الرابط"""
        self.url_var.set("")
        self.add_placeholder()
        self.update_status("URL cleared")
        self.url_entry.focus()
        self.update_progress(0)
    
    def on_entry_focus_in(self, event):
        """عند التركيز على الحقل"""
        self.clear_placeholder()
    
    def on_entry_focus_out(self, event):
        """عند فقدان التركيز"""
        if not self.url_var.get().strip():
            self.add_placeholder()
    
    def on_url_change(self, event):
        """عند تغيير الرابط"""
        url = self.url_var.get().strip()
        if url and url != "Paste YouTube URL here...":
            if "youtube.com" in url or "youtu.be" in url:
                self.update_status("✅ Valid YouTube URL")
            else:
                self.update_status("⚠️ This doesn't look like a YouTube URL")
        else:
            self.update_status("Ready")
    
    def clear_placeholder(self):
        """مسح النص التوضيحي"""
        if self.url_var.get() == "Paste YouTube URL here...":
            self.url_var.set("")
    
    def add_placeholder(self):
        """إضافة النص التوضيحي"""
        if not self.url_var.get().strip():
            self.url_var.set("Paste YouTube URL here...")
    
    def on_type_change(self):
        """عند تغيير نوع التحميل"""
        download_type = self.download_type_var.get()
        if download_type == "audio":
            self.quality_combo.config(state="disabled")
            self.audio_format_combo.config(state="readonly")
        else:
            self.quality_combo.config(state="readonly")
            self.audio_format_combo.config(state="disabled")
    
    def browse_output_directory(self):
        """اختيار مجلد الحفظ"""
        directory = filedialog.askdirectory(initialdir=self.download_path)
        if directory:
            self.download_path = directory
            self.output_label.config(text=f"Downloads will be saved to: {self.download_path}")
            self.log(f"Output directory changed to: {self.download_path}")
            self.update_status("Output directory changed")
    
    def open_downloads_folder(self):
        """فتح مجلد التحميلات"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.download_path)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open', self.download_path])  # macOS
                subprocess.run(['xdg-open', self.download_path])  # Linux
        except Exception as e:
            messagebox.showerror("Error", f"Cannot open downloads folder: {str(e)}")
    
    def install_yt_dlp(self):
        """تثبيت yt-dlp"""
        def install():
            try:
                self.update_status("Installing yt-dlp...")
                self.log("Installing yt-dlp...")
                
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
                
                self.log("✅ yt-dlp installed successfully!")
                self.update_status("yt-dlp installed - Please restart the application")
                messagebox.showinfo("Success", "yt-dlp installed successfully!\n\nPlease restart the application.")
                
            except Exception as e:
                self.log(f"❌ Failed to install yt-dlp: {str(e)}")
                self.update_status("Failed to install yt-dlp")
                messagebox.showerror("Error", f"Failed to install yt-dlp:\n{str(e)}")
        
        result = messagebox.askyesno("Install yt-dlp", 
                                   "Do you want to install yt-dlp?\n\nIt's required for downloading videos.")
        if result:
            threading.Thread(target=install, daemon=True).start()
    
    def get_video_info(self):
        """الحصول على معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url or url == "Paste YouTube URL here...":
            messagebox.showerror("Error", "Please enter a YouTube URL")
            self.url_entry.focus()
            return
        
        if not YT_DLP_AVAILABLE:
            messagebox.showerror("Error", "yt-dlp is not installed. Click 'Install yt-dlp' first.")
            return
        
        def get_info():
            try:
                self.update_status("Getting video information...")
                self.log(f"Getting info for: {url}")
                
                with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                    info = ydl.extract_info(url, download=False)
                    
                    info_text = f"""
Title: {info.get('title', 'Unknown')}
Uploader: {info.get('uploader', 'Unknown')}
Duration: {info.get('duration', 0)} seconds
Views: {info.get('view_count', 0):,}
Upload Date: {info.get('upload_date', 'Unknown')}
Video ID: {info.get('id', 'Unknown')}

Description:
{info.get('description', 'No description')[:200]}...
                    """
                    
                    # نافذة المعلومات
                    info_window = tk.Toplevel(self.root)
                    info_window.title("Video Information")
                    info_window.geometry("600x400")
                    
                    text_widget = scrolledtext.ScrolledText(info_window, wrap=tk.WORD)
                    text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                    text_widget.insert(tk.END, info_text)
                    text_widget.config(state=tk.DISABLED)
                    
                    self.update_status("Video information retrieved")
                    self.log("✅ Video information retrieved successfully")
                    
            except Exception as e:
                error_msg = str(e)
                self.log(f"❌ Error getting video info: {error_msg}")
                self.update_status("Error getting video info")
                messagebox.showerror("Error", f"Failed to get video info:\n{error_msg}")
        
        threading.Thread(target=get_info, daemon=True).start()

    def start_download(self):
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url or url == "Paste YouTube URL here...":
            messagebox.showerror("Error", "Please enter a YouTube URL")
            self.url_entry.focus()
            return

        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            self.url_entry.focus()
            return

        if not YT_DLP_AVAILABLE:
            messagebox.showerror("Error", "yt-dlp is not installed. Click 'Install yt-dlp' first.")
            return

        if self.is_downloading:
            messagebox.showwarning("Warning", "Download is already in progress")
            return

        # تعطيل أزرار التحكم
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.is_downloading = True
        self.update_progress(0)

        def download():
            try:
                download_type = self.download_type_var.get()
                quality = self.quality_var.get()
                audio_format = self.audio_format_var.get()

                self.log(f"Starting {download_type} download...")
                self.log(f"URL: {url}")
                self.log(f"Quality: {quality}")
                self.log(f"Output: {self.download_path}")

                # إعداد yt-dlp
                ydl_opts = {
                    'outtmpl': os.path.join(self.download_path, '%(title)s.%(ext)s'),
                    'progress_hooks': [self.progress_hook],
                }

                if download_type == "video":
                    self.update_status("Downloading video...")

                    # تحديد صيغة الفيديو
                    if quality == "best":
                        ydl_opts['format'] = 'best[height<=1080]'
                    elif quality == "worst":
                        ydl_opts['format'] = 'worst'
                    elif quality.endswith('p'):
                        height = quality[:-1]
                        ydl_opts['format'] = f'best[height<={height}]'
                    else:
                        ydl_opts['format'] = 'best'

                elif download_type == "audio":
                    self.update_status("Downloading audio...")

                    ydl_opts['format'] = 'bestaudio/best'
                    ydl_opts['postprocessors'] = [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': audio_format,
                        'preferredquality': '192',
                    }]

                elif download_type == "playlist":
                    self.update_status("Downloading playlist...")

                    ydl_opts['format'] = 'best[height<=1080]'
                    # إزالة noplaylist للسماح بتحميل القوائم
                    if 'noplaylist' in ydl_opts:
                        del ydl_opts['noplaylist']

                # بدء التحميل
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.download([url])

                if not self.is_downloading:  # إذا تم إيقاف التحميل
                    return

                self.update_progress(100)
                self.update_status("Download completed successfully!")
                self.log("✅ Download completed successfully!")

                # عرض رسالة نجاح
                result = messagebox.askyesno("Download Complete",
                                           "Download completed successfully!\n\nDo you want to open the downloads folder?")
                if result:
                    self.open_downloads_folder()

            except Exception as e:
                if self.is_downloading:  # فقط إذا لم يتم إيقاف التحميل
                    error_msg = str(e)
                    self.log(f"❌ Download failed: {error_msg}")
                    self.update_status("Download failed")
                    messagebox.showerror("Download Error", f"Download failed:\n\n{error_msg}")

            finally:
                # إعادة تفعيل الأزرار
                self.download_btn.config(state="normal")
                self.stop_btn.config(state="disabled")
                self.is_downloading = False

        # بدء التحميل في thread منفصل
        threading.Thread(target=download, daemon=True).start()

    def progress_hook(self, d):
        """تحديث شريط التقدم"""
        if not self.is_downloading:
            return

        if d['status'] == 'downloading':
            if 'total_bytes' in d:
                percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                self.update_progress(percent)
                self.update_status(f"Downloading... {percent:.1f}%")
            elif '_percent_str' in d:
                percent_str = d['_percent_str'].strip('%')
                try:
                    percent = float(percent_str)
                    self.update_progress(percent)
                    self.update_status(f"Downloading... {percent:.1f}%")
                except ValueError:
                    pass
        elif d['status'] == 'finished':
            filename = os.path.basename(d['filename'])
            self.log(f"✅ Downloaded: {filename}")
            self.update_status("Processing...")

    def stop_download(self):
        """إيقاف التحميل"""
        if self.is_downloading:
            self.is_downloading = False
            self.update_status("Stopping download...")
            self.log("⏹️ Download stopped by user")
            self.download_btn.config(state="normal")
            self.stop_btn.config(state="disabled")


def main():
    """تشغيل الواجهة"""
    root = tk.Tk()

    # تحسين المظهر
    try:
        root.tk.call('tk', 'scaling', 1.1)
    except:
        pass

    # التحقق من yt-dlp قبل بدء الواجهة
    if not YT_DLP_AVAILABLE:
        result = messagebox.askyesno("Missing yt-dlp",
                                   "yt-dlp is not installed.\n\n" +
                                   "It's required for downloading videos.\n\n" +
                                   "Do you want to install it now?")
        if result:
            try:
                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
                messagebox.showinfo("Success", "yt-dlp installed successfully!\n\nRestart the application to use it.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to install yt-dlp:\n{str(e)}")
            root.destroy()
            return

    app = WorkingYouTubeGUI(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
