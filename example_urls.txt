# Example YouTube URLs for batch download
# Remove the # and add real YouTube URLs

# Example format:
# https://www.youtube.com/watch?v=dQw4w9WgXcQ
# https://www.youtube.com/watch?v=9bZkp7q19f0
# https://www.youtube.com/watch?v=kJQP7kiw5Fk

# Educational content examples:
# https://www.youtube.com/watch?v=_uQrJ0TkZlc  # Python tutorial
# https://www.youtube.com/watch?v=rfscVS0vtbw  # Programming basics

# Music examples (ensure you have rights to download):
# https://www.youtube.com/watch?v=fJ9rUzIMcZQ  # Creative Commons music
# https://www.youtube.com/watch?v=ZbZSe6N_BXs  # Royalty-free music

# Playlist example:
# https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9JiYIk3HBzJyQcx

# Instructions:
# 1. Replace the example URLs with actual YouTube URLs
# 2. Remove the # at the beginning of each line
# 3. Save the file
# 4. Run: python main.py -f example_urls.txt
