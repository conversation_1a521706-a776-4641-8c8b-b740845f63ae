@echo off
chcp 65001 >nul
title أداة تحميل YouTube - تشغيل سريع

echo.
echo ========================================
echo    أداة تحميل فيديوهات YouTube
echo ========================================
echo.
echo اختر الواجهة المفضلة:
echo.
echo 1. واجهة بسيطة (نصية)
echo 2. واجهة رسومية سهلة
echo 3. واجهة رسومية متقدمة
echo 4. خروج
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo تشغيل الواجهة البسيطة...
    python simple_downloader.py
) else if "%choice%"=="2" (
    echo تشغيل الواجهة الرسومية السهلة...
    python easy_gui.py
) else if "%choice%"=="3" (
    echo تشغيل الواجهة الرسومية المتقدمة...
    python gui.py
) else if "%choice%"=="4" (
    echo وداعاً!
    exit
) else (
    echo اختيار غير صحيح
    pause
    goto :eof
)

echo.
echo تم إغلاق البرنامج
pause
