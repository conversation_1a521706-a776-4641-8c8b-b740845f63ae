#!/usr/bin/env python3
"""
أداة تحميل بسيطة من YouTube
واجهة سهلة لإدخال الروابط وتحميل الفيديوهات
"""

import os
from pathlib import Path
from downloader import YouTubeDownloader
from colorama import init, Fore, Style

# تهيئة الألوان
init(autoreset=True)

def print_header():
    """طباعة رأس البرنامج"""
    print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                  أداة تحميل فيديوهات YouTube                ║
║                        واجهة بسيطة                          ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
""")

def get_youtube_url():
    """الحصول على رابط YouTube من المستخدم"""
    print(f"{Fore.YELLOW}📝 أدخل رابط الفيديو من YouTube:{Style.RESET_ALL}")
    print(f"{Fore.CYAN}مثال: https://www.youtube.com/watch?v=dQw4w9WgXcQ{Style.RESET_ALL}")
    print(f"{Fore.GREEN}أو: https://youtu.be/dQw4w9WgXcQ{Style.RESET_ALL}")
    print()
    
    while True:
        url = input(f"{Fore.WHITE}الرابط: {Style.RESET_ALL}").strip()
        
        if not url:
            print(f"{Fore.RED}❌ يرجى إدخال رابط صحيح{Style.RESET_ALL}")
            continue
        
        # التحقق من صحة الرابط
        if "youtube.com" in url or "youtu.be" in url:
            return url
        else:
            print(f"{Fore.RED}❌ هذا ليس رابط YouTube صحيح{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}يجب أن يحتوي الرابط على youtube.com أو youtu.be{Style.RESET_ALL}")

def choose_download_type():
    """اختيار نوع التحميل"""
    print(f"\n{Fore.CYAN}📋 اختر نوع التحميل:{Style.RESET_ALL}")
    print("1. 🎥 فيديو (مع الصوت)")
    print("2. 🎵 صوت فقط (MP3)")
    print("3. 📋 قائمة تشغيل كاملة")
    
    while True:
        choice = input(f"\n{Fore.YELLOW}اختر (1-3): {Style.RESET_ALL}").strip()
        
        if choice == "1":
            return "video"
        elif choice == "2":
            return "audio"
        elif choice == "3":
            return "playlist"
        else:
            print(f"{Fore.RED}❌ اختيار غير صحيح. اختر 1 أو 2 أو 3{Style.RESET_ALL}")

def choose_quality():
    """اختيار جودة الفيديو"""
    print(f"\n{Fore.CYAN}📺 اختر جودة الفيديو:{Style.RESET_ALL}")
    print("1. أعلى جودة متاحة")
    print("2. 1080p (Full HD)")
    print("3. 720p (HD)")
    print("4. 480p (SD)")
    print("5. أقل جودة (لتوفير المساحة)")
    
    while True:
        choice = input(f"\n{Fore.YELLOW}اختر (1-5): {Style.RESET_ALL}").strip()
        
        quality_map = {
            "1": "highest",
            "2": "1080p",
            "3": "720p", 
            "4": "480p",
            "5": "lowest"
        }
        
        if choice in quality_map:
            return quality_map[choice]
        else:
            print(f"{Fore.RED}❌ اختيار غير صحيح. اختر رقم من 1 إلى 5{Style.RESET_ALL}")

def progress_callback(stream, chunk, bytes_remaining):
    """عرض تقدم التحميل"""
    total_size = stream.filesize
    bytes_downloaded = total_size - bytes_remaining
    percentage = (bytes_downloaded / total_size) * 100
    
    # عرض شريط التقدم
    bar_length = 30
    filled_length = int(bar_length * bytes_downloaded // total_size)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    
    print(f'\r{Fore.GREEN}📥 التحميل: |{bar}| {percentage:.1f}% مكتمل{Style.RESET_ALL}', end='', flush=True)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # إنشاء مجلد التحميل
    download_path = "تحميلات_YouTube"
    Path(download_path).mkdir(exist_ok=True)
    
    # إنشاء أداة التحميل
    downloader = YouTubeDownloader(download_path)
    
    try:
        while True:
            # الحصول على الرابط
            url = get_youtube_url()
            
            # اختيار نوع التحميل
            download_type = choose_download_type()
            
            # اختيار الجودة (للفيديو فقط)
            if download_type == "video":
                quality = choose_quality()
            else:
                quality = "highest"
            
            print(f"\n{Fore.GREEN}🚀 بدء التحميل...{Style.RESET_ALL}")
            
            try:
                # عرض معلومات الفيديو أولاً
                info = downloader.get_video_info(url)
                print(f"\n{Fore.CYAN}📹 معلومات الفيديو:{Style.RESET_ALL}")
                print(f"   العنوان: {info['title']}")
                print(f"   القناة: {info['author']}")
                print(f"   المدة: {info['length']} ثانية")
                print(f"   المشاهدات: {info['views']:,}")
                
                # التحميل
                if download_type == "video":
                    filepath = downloader.download_video(url, quality, progress_callback)
                    print(f"\n\n{Fore.GREEN}✅ تم تحميل الفيديو بنجاح!{Style.RESET_ALL}")
                    
                elif download_type == "audio":
                    filepath = downloader.download_audio(url, "mp3", progress_callback)
                    print(f"\n\n{Fore.GREEN}✅ تم تحميل الصوت بنجاح!{Style.RESET_ALL}")
                    
                elif download_type == "playlist":
                    print(f"{Fore.YELLOW}📋 تحميل قائمة التشغيل...{Style.RESET_ALL}")
                    filepaths = downloader.download_playlist(url, "video", quality)
                    print(f"\n{Fore.GREEN}✅ تم تحميل {len(filepaths)} فيديو من القائمة!{Style.RESET_ALL}")
                    filepath = f"تم تحميل {len(filepaths)} ملف"
                
                print(f"   📁 مكان الحفظ: {download_path}")
                print(f"   📄 اسم الملف: {Path(filepath).name if isinstance(filepath, str) else 'ملفات متعددة'}")
                
            except Exception as e:
                print(f"\n{Fore.RED}❌ خطأ في التحميل: {str(e)}{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}💡 تأكد من:{Style.RESET_ALL}")
                print("   - صحة الرابط")
                print("   - اتصال الإنترنت")
                print("   - أن الفيديو متاح للتحميل")
            
            # سؤال عن تحميل آخر
            print(f"\n{Fore.CYAN}هل تريد تحميل فيديو آخر؟{Style.RESET_ALL}")
            another = input(f"{Fore.YELLOW}اكتب 'نعم' أو 'y' للمتابعة، أي شيء آخر للخروج: {Style.RESET_ALL}").strip().lower()
            
            if another not in ['نعم', 'y', 'yes', 'ن']:
                break
            
            print("\n" + "="*60 + "\n")
    
    except KeyboardInterrupt:
        print(f"\n\n{Fore.YELLOW}⚠️ تم إيقاف البرنامج بواسطة المستخدم{Style.RESET_ALL}")
    
    except Exception as e:
        print(f"\n{Fore.RED}❌ خطأ غير متوقع: {str(e)}{Style.RESET_ALL}")
    
    finally:
        print(f"\n{Fore.GREEN}🙏 شكراً لاستخدام أداة تحميل YouTube!{Style.RESET_ALL}")
        input(f"{Fore.CYAN}اضغط Enter للخروج...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
