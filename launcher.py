#!/usr/bin/env python3
"""
YouTube Downloader Launcher
Simple launcher to choose between different interfaces
"""

import sys
import subprocess
from pathlib import Path
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)


def print_banner():
    """Print application banner"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    YouTube Video Downloader                  ║
║                         Launcher                             ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


def check_files():
    """Check if required files exist"""
    required_files = ['main.py', 'gui.py', 'downloader.py', 'advanced_features.py']
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"{Fore.RED}❌ Missing required files:{Style.RESET_ALL}")
        for file in missing_files:
            print(f"   - {file}")
        print(f"\n{Fore.YELLOW}Please ensure all files are in the same directory.{Style.RESET_ALL}")
        return False
    
    return True


def launch_application(script_name, description):
    """Launch a specific application"""
    print(f"\n{Fore.GREEN}🚀 Launching {description}...{Style.RESET_ALL}")
    try:
        subprocess.run([sys.executable, script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"{Fore.RED}❌ Error launching {description}: {e}{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️  {description} was interrupted by user{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")


def show_quick_help():
    """Show quick help information"""
    help_text = f"""
{Fore.CYAN}📖 Quick Help:{Style.RESET_ALL}

{Fore.YELLOW}Command Line Interface (CLI):{Style.RESET_ALL}
- Best for: Automation, scripting, advanced users
- Features: Full command-line control, batch processing
- Usage: Supports all features via command-line arguments

{Fore.YELLOW}Graphical User Interface (GUI):{Style.RESET_ALL}
- Best for: Beginners, visual interface preference
- Features: Easy-to-use interface, progress bars, drag-and-drop
- Usage: Point-and-click interface with visual feedback

{Fore.YELLOW}Advanced Features:{Style.RESET_ALL}
- Best for: Power users, batch operations
- Features: Subtitle download, format conversion, configuration
- Usage: Advanced batch processing and customization

{Fore.YELLOW}Installation:{Style.RESET_ALL}
- Run if you haven't installed dependencies yet
- Automatically sets up the environment
- Tests installation and shows usage examples
"""
    print(help_text)


def main():
    """Main launcher function"""
    print_banner()
    
    # Check if required files exist
    if not check_files():
        return
    
    while True:
        print(f"\n{Fore.CYAN}🔧 Choose an interface:{Style.RESET_ALL}")
        print("1. 💻 Command Line Interface (Interactive)")
        print("2. 🖥️  Graphical User Interface")
        print("3. ⚙️  Advanced Features")
        print("4. 🛠️  Run Installation/Setup")
        print("5. 📖 Quick Help")
        print("6. 🚪 Exit")
        
        choice = input(f"\n{Fore.YELLOW}Enter your choice (1-6): {Style.RESET_ALL}").strip()
        
        try:
            if choice == '1':
                launch_application('main.py', 'Command Line Interface')
            
            elif choice == '2':
                launch_application('gui.py', 'Graphical User Interface')
            
            elif choice == '3':
                launch_application('advanced_features.py', 'Advanced Features')
            
            elif choice == '4':
                launch_application('install.py', 'Installation Script')
            
            elif choice == '5':
                show_quick_help()
            
            elif choice == '6':
                print(f"\n{Fore.GREEN}👋 Thank you for using YouTube Downloader!{Style.RESET_ALL}")
                break
            
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter a number between 1-6.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}⚠️  Launcher interrupted by user.{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"\n{Fore.RED}❌ Unexpected error: {str(e)}{Style.RESET_ALL}")


if __name__ == "__main__":
    main()
