@echo off
chcp 65001 >nul
title إلغاء تثبيت YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║               🗑️ إلغاء تثبيت YouTube Video Downloader        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo ⚠️ هذا سيقوم بإزالة YouTube Video Downloader من جهازك نهائياً
echo.

echo 🔍 البحث عن ملفات البرنامج...

REM المسارات المحتملة للتثبيت
set "INSTALL_PATH1=%LOCALAPPDATA%\YouTube Video Downloader"
set "INSTALL_PATH2=%PROGRAMFILES%\YouTube Video Downloader"
set "INSTALL_PATH3=%PROGRAMFILES(X86)%\YouTube Video Downloader"

set FOUND=0

if exist "%INSTALL_PATH1%" (
    echo ✅ تم العثور على البرنامج في: %INSTALL_PATH1%
    set "MAIN_INSTALL=%INSTALL_PATH1%"
    set FOUND=1
)

if exist "%INSTALL_PATH2%" (
    echo ✅ تم العثور على البرنامج في: %INSTALL_PATH2%
    set "MAIN_INSTALL=%INSTALL_PATH2%"
    set FOUND=1
)

if exist "%INSTALL_PATH3%" (
    echo ✅ تم العثور على البرنامج في: %INSTALL_PATH3%
    set "MAIN_INSTALL=%INSTALL_PATH3%"
    set FOUND=1
)

if %FOUND%==0 (
    echo ❌ لم يتم العثور على البرنامج في المسارات المعتادة
    echo.
    echo 💡 هل تريد البحث في مسار مخصص؟ (y/n)
    set /p custom_search="اختر: "
    
    if /i "%custom_search%"=="y" (
        echo 📁 أدخل مسار البرنامج:
        set /p "CUSTOM_PATH=المسار: "
        
        if exist "%CUSTOM_PATH%\YouTube_Video_Downloader.exe" (
            set "MAIN_INSTALL=%CUSTOM_PATH%"
            set FOUND=1
            echo ✅ تم العثور على البرنامج في: %CUSTOM_PATH%
        ) else (
            echo ❌ لم يتم العثور على البرنامج في المسار المحدد
        )
    )
    
    if %FOUND%==0 (
        echo.
        echo ❌ لا يمكن المتابعة بدون العثور على البرنامج
        pause
        exit /b 1
    )
)

echo.
echo 📋 سيتم حذف:
echo    📁 مجلد البرنامج: %MAIN_INSTALL%
echo    🔗 اختصار سطح المكتب
echo    📋 إدخالات قائمة ابدأ
echo    📄 ملفات التكوين
echo.

echo ⚠️ هذا الإجراء لا يمكن التراجع عنه!
echo.
echo هل أنت متأكد من إلغاء التثبيت؟ (y/n)
set /p confirm="اختر: "

if /i not "%confirm%"=="y" (
    echo ❌ تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo ⏳ جاري إلغاء التثبيت...
echo.

REM إيقاف البرنامج إذا كان يعمل
echo 🛑 إيقاف البرنامج إذا كان يعمل...
taskkill /f /im "YouTube_Video_Downloader.exe" >nul 2>&1

REM حذف اختصار سطح المكتب
echo 🔗 حذف اختصار سطح المكتب...
del "%USERPROFILE%\Desktop\YouTube Video Downloader.bat" >nul 2>&1
del "%USERPROFILE%\Desktop\YouTube Video Downloader.lnk" >nul 2>&1

REM حذف إدخالات قائمة ابدأ
echo 📋 حذف إدخالات قائمة ابدأ...
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\YouTube Video Downloader" >nul 2>&1

REM حذف ملفات البرنامج
echo 📁 حذف ملفات البرنامج...
rmdir /s /q "%MAIN_INSTALL%" >nul 2>&1

REM التحقق من نجاح الحذف
if exist "%MAIN_INSTALL%" (
    echo ⚠️ لم يتم حذف بعض الملفات (قد تكون قيد الاستخدام)
    echo.
    echo 💡 جرب:
    echo    1. أعد تشغيل الكمبيوتر وأعد تشغيل أداة إلغاء التثبيت
    echo    2. احذف المجلد يدوياً: %MAIN_INSTALL%
    echo.
) else (
    echo ✅ تم حذف ملفات البرنامج
)

REM حذف ملفات إضافية محتملة
echo 🧹 تنظيف الملفات الإضافية...

REM حذف ملفات التكوين في AppData
if exist "%APPDATA%\YouTube Video Downloader" (
    rmdir /s /q "%APPDATA%\YouTube Video Downloader" >nul 2>&1
)

REM حذف ملفات مؤقتة
if exist "%TEMP%\YouTube_Video_Downloader*" (
    del /q "%TEMP%\YouTube_Video_Downloader*" >nul 2>&1
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 ✅ تم إلغاء التثبيت بنجاح!                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 تم إلغاء تثبيت YouTube Video Downloader بنجاح!
echo.

echo 📊 ملخص ما تم حذفه:
if not exist "%MAIN_INSTALL%" echo    ✅ مجلد البرنامج
if not exist "%USERPROFILE%\Desktop\YouTube Video Downloader.bat" echo    ✅ اختصار سطح المكتب
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\YouTube Video Downloader" echo    ✅ إدخالات قائمة ابدأ

echo.
echo 💡 ملاحظات:
echo    - تم الاحتفاظ بالفيديوهات المحملة
echo    - يمكنك إعادة تثبيت البرنامج في أي وقت
echo    - شكراً لاستخدام YouTube Video Downloader!
echo.

echo هل تريد حذف أداة إلغاء التثبيت هذه أيضاً؟ (y/n)
set /p delete_self="اختر: "

if /i "%delete_self%"=="y" (
    echo.
    echo 🗑️ حذف أداة إلغاء التثبيت...
    
    REM إنشاء ملف batch مؤقت لحذف نفسه
    echo @echo off > "%TEMP%\cleanup_uninstaller.bat"
    echo timeout /t 2 /nobreak ^>nul >> "%TEMP%\cleanup_uninstaller.bat"
    echo del "%~f0" >> "%TEMP%\cleanup_uninstaller.bat"
    echo del "%%~f0" >> "%TEMP%\cleanup_uninstaller.bat"
    
    start "" "%TEMP%\cleanup_uninstaller.bat"
    exit
)

echo.
echo 🙏 شكراً لاستخدام YouTube Video Downloader!
echo.
pause
