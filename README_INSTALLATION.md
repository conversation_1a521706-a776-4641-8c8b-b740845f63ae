# 🛠️ دليل التثبيت الشامل - YouTube Video Downloader

## 🎯 أدوات التثبيت المتاحة

تم إنشاء عدة أدوات تثبيت مختلفة لتناسب جميع الاحتياجات والمستويات التقنية.

---

## 🚀 الطريقة الأسهل - التشغيل الموحد

### **أدوات_التثبيت.bat** ⭐ (الأفضل)
```bash
أدوات_التثبيت.bat
```
**المميزات:**
- ✅ **واجهة موحدة** لجميع أدوات التثبيت
- ✅ **اختيار تلقائي** للأداة المناسبة
- ✅ **فحص المتطلبات** قبل التثبيت
- ✅ **بناء البرنامج** إذا لم يكن موجوداً

---

## 📋 أدوات التثبيت المتاحة:

### 1. **التثبيت السريع** ⚡
```bash
تثبيت_البرنامج.bat
```
**الوصف:** تثبيت سريع وبسيط
**المدة:** 1-2 دقيقة
**المميزات:**
- ✅ تثبيت في مجلد البرامج
- ✅ اختصار سطح المكتب
- ✅ إضافة لقائمة ابدأ
- ✅ أداة إلغاء تثبيت

**الاستخدام:**
1. شغّل `تثبيت_البرنامج.bat`
2. اختر "1" للتثبيت السريع
3. اضغط "y" للتأكيد
4. انتظر انتهاء التثبيت

### 2. **التثبيت المخصص** 🎯
```bash
تثبيت_البرنامج.bat
```
**الوصف:** تثبيت مع خيارات مخصصة
**المدة:** 2-3 دقائق
**المميزات:**
- ✅ اختيار مكان التثبيت
- ✅ اختيار الاختصارات
- ✅ تحكم كامل في الخيارات

**الاستخدام:**
1. شغّل `تثبيت_البرنامج.bat`
2. اختر "2" للتثبيت المخصص
3. أدخل المسار المطلوب
4. اختر الاختصارات المطلوبة

### 3. **أداة التثبيت الرسومية** 🖥️
```bash
python installer.py
```
**الوصف:** واجهة رسومية احترافية
**المدة:** 2-4 دقائق
**المتطلبات:** Python مثبت
**المميزات:**
- ✅ واجهة رسومية جميلة
- ✅ خيارات متقدمة
- ✅ شريط تقدم
- ✅ رسائل واضحة

**الاستخدام:**
1. تأكد من تثبيت Python
2. شغّل `python installer.py`
3. اتبع التعليمات على الشاشة

### 4. **النسخة المحمولة** 💻
```bash
أدوات_التثبيت.bat → خيار 4
```
**الوصف:** نسخة لا تحتاج تثبيت
**المدة:** 30 ثانية
**المميزات:**
- ✅ لا تحتاج تثبيت
- ✅ يمكن نقلها على فلاش ميموري
- ✅ لا تؤثر على النظام
- ✅ مناسبة للاستخدام المؤقت

**الاستخدام:**
1. اختر مجلد للنسخة المحمولة
2. انتظر نسخ الملفات
3. شغّل `تشغيل.bat` أو الملف التنفيذي مباشرة

### 5. **أداة Inno Setup** 🔧 (متقدم)
```bash
iscc installer_setup.iss
```
**الوصف:** أداة تثبيت احترافية
**المدة:** 5-10 دقائق للبناء
**المتطلبات:** Inno Setup مثبت
**المميزات:**
- ✅ أداة تثبيت احترافية
- ✅ واجهة Windows معيارية
- ✅ دعم متعدد اللغات
- ✅ مناسب للتوزيع التجاري

**الاستخدام:**
1. ثبّت Inno Setup من [الموقع الرسمي](https://jrsoftware.org/isinfo.php)
2. شغّل `iscc installer_setup.iss`
3. ستجد الملف في `setup_output/`

---

## 🗑️ إلغاء التثبيت

### **إلغاء_التثبيت.bat**
```bash
إلغاء_التثبيت.bat
```
**المميزات:**
- ✅ **بحث تلقائي** عن ملفات البرنامج
- ✅ **حذف شامل** لجميع الملفات والاختصارات
- ✅ **تنظيف النظام** من الملفات المؤقتة
- ✅ **تأكيد قبل الحذف** لتجنب الأخطاء

**الاستخدام:**
1. شغّل `إلغاء_التثبيت.bat`
2. اضغط "y" للتأكيد
3. انتظر انتهاء عملية الحذف

---

## 📊 مقارنة أدوات التثبيت

| الأداة | السهولة | السرعة | الخيارات | المتطلبات |
|--------|---------|---------|----------|------------|
| **التثبيت السريع** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | لا شيء |
| **التثبيت المخصص** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | لا شيء |
| **الأداة الرسومية** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Python |
| **النسخة المحمولة** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | لا شيء |
| **Inno Setup** | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ | Inno Setup |

---

## 🎯 أيهما تختار؟

### **للمستخدم العادي:**
```bash
أدوات_التثبيت.bat → خيار 1 (التثبيت السريع)
```

### **للمستخدم المتقدم:**
```bash
python installer.py
```

### **للاستخدام المؤقت:**
```bash
أدوات_التثبيت.bat → خيار 4 (النسخة المحمولة)
```

### **للتوزيع التجاري:**
```bash
iscc installer_setup.iss
```

---

## 📁 ملفات التثبيت

```
📦 أدوات التثبيت/
├── 🚀 أدوات_التثبيت.bat          # الأداة الموحدة (الأفضل)
├── ⚡ تثبيت_البرنامج.bat         # التثبيت السريع/المخصص
├── 🖥️ installer.py              # الأداة الرسومية
├── 🔧 installer_setup.iss       # ملف Inno Setup
├── 🗑️ إلغاء_التثبيت.bat        # أداة إلغاء التثبيت
└── 📋 README_INSTALLATION.md    # هذا الدليل
```

---

## 🛠️ خطوات التثبيت التفصيلية

### **الطريقة الأسهل (مستحسنة):**

1. **تشغيل الأداة الموحدة:**
   ```bash
   أدوات_التثبيت.bat
   ```

2. **اختيار نوع التثبيت:**
   - اضغط "1" للتثبيت السريع
   - أو اضغط "2" للتثبيت المخصص

3. **اتباع التعليمات:**
   - اقرأ الرسائل على الشاشة
   - اضغط "y" للتأكيد
   - انتظر انتهاء التثبيت

4. **التشغيل:**
   - ابحث عن "YouTube" في قائمة ابدأ
   - أو انقر على الاختصار في سطح المكتب

---

## 🔧 حل المشاكل الشائعة

### **خطأ: "الملف التنفيذي غير موجود"**
```bash
# الحل
بناء_exe_بسيط.bat
```

### **خطأ: "Python غير مثبت"**
```bash
# الحل
1. حمّل Python من python.org
2. أو استخدم التثبيت البسيط بدلاً من الرسومي
```

### **خطأ: "فشل في التثبيت"**
```bash
# الحلول
1. شغّل كمدير (Run as administrator)
2. تأكد من مساحة القرص (100MB+)
3. أغلق برامج الحماية مؤقتاً
```

### **خطأ: "لا يمكن إنشاء الاختصارات"**
```bash
# الحل
1. تأكد من صلاحيات الكتابة
2. شغّل كمدير
3. أو استخدم النسخة المحمولة
```

---

## 📋 ما بعد التثبيت

### **التحقق من التثبيت:**
1. ✅ ابحث عن البرنامج في قائمة ابدأ
2. ✅ تأكد من وجود الاختصار على سطح المكتب
3. ✅ جرب تشغيل البرنامج

### **الاستخدام الأول:**
1. 🎥 الصق رابط فيديو YouTube
2. ⚙️ اختر نوع التحميل والجودة
3. ⬇️ اضغط "Download"

### **إلغاء التثبيت (إذا لزم الأمر):**
```bash
إلغاء_التثبيت.bat
```

---

## 🎉 النتيجة النهائية

بعد التثبيت ستحصل على:

✅ **برنامج مثبت بشكل صحيح** في النظام
✅ **اختصار على سطح المكتب** للوصول السريع
✅ **إدخال في قائمة ابدأ** للبحث السهل
✅ **أداة إلغاء تثبيت** للحذف الآمن
✅ **جميع الملفات المطلوبة** في مكان واحد

**الآن يمكنك تحميل فيديوهاتك المفضلة بسهولة!** 🚀

---

## 💡 نصائح إضافية

### **للحصول على أفضل تجربة:**
1. 🔄 استخدم أحدث إصدار من Windows
2. 🛡️ أضف استثناء في برنامج الحماية
3. 💾 تأكد من مساحة القرص الكافية
4. 🌐 استخدم اتصال إنترنت مستقر

### **للمشاركة مع الآخرين:**
1. 📦 استخدم النسخة المحمولة للمشاركة السريعة
2. 🔧 استخدم Inno Setup للتوزيع الاحترافي
3. 📋 شارك هذا الدليل مع التعليمات

**استمتع بتحميل فيديوهاتك!** 🎬✨
