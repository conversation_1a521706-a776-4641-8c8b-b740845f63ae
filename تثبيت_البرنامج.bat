@echo off
chcp 65001 >nul
title تثبيت YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🏗️ تثبيت YouTube Video Downloader             ║
echo ║                     أداة التثبيت السريعة                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من الملفات المطلوبة...

if not exist "dist\YouTube_Video_Downloader.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo.
    echo 💡 تأكد من بناء البرنامج أولاً:
    echo    بناء_exe_بسيط.bat
    echo.
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود

REM حساب حجم الملف
for %%A in ("dist\YouTube_Video_Downloader.exe") do (
    set size=%%~zA
    set /a size_mb=!size!/1024/1024
    echo 📊 حجم البرنامج: !size_mb! MB
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     📋 خيارات التثبيت                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo اختر نوع التثبيت:
echo.
echo 1. 🚀 تثبيت سريع (مستحسن)
echo    - تثبيت في مجلد البرامج
echo    - إنشاء اختصار على سطح المكتب
echo    - إضافة إلى قائمة ابدأ
echo.
echo 2. 🎯 تثبيت مخصص
echo    - اختيار مكان التثبيت
echo    - اختيار الاختصارات
echo.
echo 3. 💻 تثبيت محمول
echo    - نسخ إلى مجلد محدد
echo    - بدون اختصارات نظام
echo.
echo 4. ❌ إلغاء
echo.

set /p install_type="اختر رقم (1-4): "

if "%install_type%"=="1" goto QUICK_INSTALL
if "%install_type%"=="2" goto CUSTOM_INSTALL
if "%install_type%"=="3" goto PORTABLE_INSTALL
if "%install_type%"=="4" goto CANCEL
goto QUICK_INSTALL

:QUICK_INSTALL
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     🚀 التثبيت السريع                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "INSTALL_DIR=%LOCALAPPDATA%\YouTube Video Downloader"
echo 📁 مكان التثبيت: %INSTALL_DIR%
echo.

echo هل تريد المتابعة؟ (y/n)
set /p confirm="اختر: "

if /i not "%confirm%"=="y" goto CANCEL

echo.
echo ⏳ جاري التثبيت...

REM إنشاء مجلد التثبيت
echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM نسخ الملفات
echo 📋 نسخ الملفات...
copy "dist\YouTube_Video_Downloader.exe" "%INSTALL_DIR%\" >nul
if exist "dist\README.txt" copy "dist\README.txt" "%INSTALL_DIR%\" >nul
if exist "youtube_downloader_icon.png" copy "youtube_downloader_icon.png" "%INSTALL_DIR%\icon.png" >nul

REM إنشاء اختصار سطح المكتب
echo 🔗 إنشاء اختصار سطح المكتب...
set "SHORTCUT_PATH=%USERPROFILE%\Desktop\YouTube Video Downloader.bat"
echo @echo off > "%SHORTCUT_PATH%"
echo title YouTube Video Downloader >> "%SHORTCUT_PATH%"
echo cd /d "%INSTALL_DIR%" >> "%SHORTCUT_PATH%"
echo start "" "YouTube_Video_Downloader.exe" >> "%SHORTCUT_PATH%"

REM إنشاء مجلد في قائمة ابدأ
echo 📋 إضافة إلى قائمة ابدأ...
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs\YouTube Video Downloader"
if not exist "%START_MENU%" mkdir "%START_MENU%"

REM اختصار البرنامج الرئيسي
echo @echo off > "%START_MENU%\YouTube Video Downloader.bat"
echo cd /d "%INSTALL_DIR%" >> "%START_MENU%\YouTube Video Downloader.bat"
echo start "" "YouTube_Video_Downloader.exe" >> "%START_MENU%\YouTube Video Downloader.bat"

REM اختصار إلغاء التثبيت
echo @echo off > "%START_MENU%\إلغاء التثبيت.bat"
echo title إلغاء تثبيت YouTube Video Downloader >> "%START_MENU%\إلغاء التثبيت.bat"
echo echo جاري إلغاء التثبيت... >> "%START_MENU%\إلغاء التثبيت.bat"
echo del "%USERPROFILE%\Desktop\YouTube Video Downloader.bat" 2^>nul >> "%START_MENU%\إلغاء التثبيت.bat"
echo rmdir /s /q "%INSTALL_DIR%" >> "%START_MENU%\إلغاء التثبيت.bat"
echo rmdir /s /q "%START_MENU%" >> "%START_MENU%\إلغاء التثبيت.bat"
echo echo تم إلغاء التثبيت بنجاح! >> "%START_MENU%\إلغاء التثبيت.bat"
echo pause >> "%START_MENU%\إلغاء التثبيت.bat"

REM إنشاء ملف معلومات التثبيت
echo [Installation Info] > "%INSTALL_DIR%\install_info.txt"
echo Program=YouTube Video Downloader >> "%INSTALL_DIR%\install_info.txt"
echo Version=1.0 >> "%INSTALL_DIR%\install_info.txt"
echo InstallDate=%DATE% %TIME% >> "%INSTALL_DIR%\install_info.txt"
echo InstallPath=%INSTALL_DIR% >> "%INSTALL_DIR%\install_info.txt"

goto SUCCESS

:CUSTOM_INSTALL
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     🎯 التثبيت المخصص                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📁 أدخل مسار التثبيت (أو اضغط Enter للافتراضي):
echo الافتراضي: %LOCALAPPDATA%\YouTube Video Downloader
set /p "CUSTOM_PATH=المسار: "

if "%CUSTOM_PATH%"=="" set "CUSTOM_PATH=%LOCALAPPDATA%\YouTube Video Downloader"
set "INSTALL_DIR=%CUSTOM_PATH%"

echo.
echo 🔗 خيارات الاختصارات:
echo.
echo هل تريد إنشاء اختصار على سطح المكتب؟ (y/n)
set /p desktop_shortcut="اختر: "

echo هل تريد إضافة إلى قائمة ابدأ؟ (y/n)
set /p start_menu="اختر: "

echo.
echo 📋 ملخص التثبيت:
echo    المسار: %INSTALL_DIR%
echo    اختصار سطح المكتب: %desktop_shortcut%
echo    قائمة ابدأ: %start_menu%
echo.

echo هل تريد المتابعة؟ (y/n)
set /p confirm="اختر: "

if /i not "%confirm%"=="y" goto CANCEL

echo.
echo ⏳ جاري التثبيت المخصص...

REM إنشاء مجلد التثبيت
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM نسخ الملفات
copy "dist\YouTube_Video_Downloader.exe" "%INSTALL_DIR%\" >nul
if exist "dist\README.txt" copy "dist\README.txt" "%INSTALL_DIR%\" >nul

REM اختصار سطح المكتب (إذا طُلب)
if /i "%desktop_shortcut%"=="y" (
    echo 🔗 إنشاء اختصار سطح المكتب...
    set "SHORTCUT_PATH=%USERPROFILE%\Desktop\YouTube Video Downloader.bat"
    echo @echo off > "%SHORTCUT_PATH%"
    echo cd /d "%INSTALL_DIR%" >> "%SHORTCUT_PATH%"
    echo start "" "YouTube_Video_Downloader.exe" >> "%SHORTCUT_PATH%"
)

REM قائمة ابدأ (إذا طُلب)
if /i "%start_menu%"=="y" (
    echo 📋 إضافة إلى قائمة ابدأ...
    set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs\YouTube Video Downloader"
    if not exist "%START_MENU%" mkdir "%START_MENU%"
    
    echo @echo off > "%START_MENU%\YouTube Video Downloader.bat"
    echo cd /d "%INSTALL_DIR%" >> "%START_MENU%\YouTube Video Downloader.bat"
    echo start "" "YouTube_Video_Downloader.exe" >> "%START_MENU%\YouTube Video Downloader.bat"
)

goto SUCCESS

:PORTABLE_INSTALL
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     💻 التثبيت المحمول                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📁 أدخل مسار المجلد المحمول:
echo مثال: D:\Programs\YouTube Downloader
set /p "PORTABLE_PATH=المسار: "

if "%PORTABLE_PATH%"=="" (
    echo ❌ يجب إدخال مسار صحيح
    pause
    goto PORTABLE_INSTALL
)

set "INSTALL_DIR=%PORTABLE_PATH%"

echo.
echo 📋 سيتم إنشاء نسخة محمولة في: %INSTALL_DIR%
echo هل تريد المتابعة؟ (y/n)
set /p confirm="اختر: "

if /i not "%confirm%"=="y" goto CANCEL

echo.
echo ⏳ جاري إنشاء النسخة المحمولة...

REM إنشاء المجلد
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM نسخ جميع الملفات
copy "dist\YouTube_Video_Downloader.exe" "%INSTALL_DIR%\" >nul
if exist "dist\README.txt" copy "dist\README.txt" "%INSTALL_DIR%\" >nul
if exist "youtube_downloader_icon.png" copy "youtube_downloader_icon.png" "%INSTALL_DIR%\" >nul

REM إنشاء ملف تشغيل
echo @echo off > "%INSTALL_DIR%\تشغيل.bat"
echo title YouTube Video Downloader - Portable >> "%INSTALL_DIR%\تشغيل.bat"
echo cd /d "%%~dp0" >> "%INSTALL_DIR%\تشغيل.bat"
echo start "" "YouTube_Video_Downloader.exe" >> "%INSTALL_DIR%\تشغيل.bat"

REM إنشاء ملف README للنسخة المحمولة
echo YouTube Video Downloader - Portable Version > "%INSTALL_DIR%\README_PORTABLE.txt"
echo ============================================== >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo. >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo هذه نسخة محمولة من YouTube Video Downloader >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo. >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo للتشغيل: >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo - انقر مزدوج على "تشغيل.bat" >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo - أو انقر مزدوج على "YouTube_Video_Downloader.exe" >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo. >> "%INSTALL_DIR%\README_PORTABLE.txt"
echo يمكنك نسخ هذا المجلد إلى أي مكان أو فلاش ميموري >> "%INSTALL_DIR%\README_PORTABLE.txt"

goto SUCCESS

:SUCCESS
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ تم التثبيت بنجاح!                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 تم تثبيت YouTube Video Downloader بنجاح!
echo.
echo 📁 مكان التثبيت: %INSTALL_DIR%
echo.

if exist "%USERPROFILE%\Desktop\YouTube Video Downloader.bat" (
    echo ✅ تم إنشاء اختصار على سطح المكتب
)

if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\YouTube Video Downloader" (
    echo ✅ تم إضافة البرنامج إلى قائمة ابدأ
)

echo.
echo 💡 يمكنك الآن:
echo    - تشغيل البرنامج من سطح المكتب
echo    - البحث عن "YouTube" في قائمة ابدأ
echo    - تشغيل الملف مباشرة من مجلد التثبيت
echo.

echo هل تريد تشغيل البرنامج الآن؟ (y/n)
set /p run_now="اختر: "

if /i "%run_now%"=="y" (
    echo 🚀 تشغيل البرنامج...
    cd /d "%INSTALL_DIR%"
    start "" "YouTube_Video_Downloader.exe"
)

echo.
echo 🙏 شكراً لاستخدام YouTube Video Downloader!
goto END

:CANCEL
echo.
echo ❌ تم إلغاء التثبيت
echo.

:END
pause
