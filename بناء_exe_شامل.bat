@echo off
chcp 65001 >nul
title بناء ملف exe شامل - YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🏗️ بناء ملف exe شامل                         ║
echo ║              YouTube Video Downloader                        ║
echo ║                  جميع الخيارات المتاحة                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من:
    echo    https://python.org/downloads/
    echo.
    echo ⚠️ تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION%

REM التحقق من pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo جاري إصلاح pip...
    python -m ensurepip --upgrade
)

echo ✅ pip متوفر

REM التحقق من الملفات المطلوبة
echo.
echo 📁 التحقق من الملفات...

set MISSING_FILES=0

if not exist "gui_working.py" (
    echo ❌ gui_working.py مفقود
    set MISSING_FILES=1
)

if not exist "downloader.py" (
    echo ❌ downloader.py مفقود  
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo.
    echo ❌ ملفات مطلوبة مفقودة
    echo تأكد من وجود جميع ملفات البرنامج
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    📦 تثبيت المكتبات                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔄 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 📦 تثبيت المكتبات الأساسية...

set PACKAGES=pyinstaller yt-dlp Pillow colorama tqdm

for %%p in (%PACKAGES%) do (
    echo    تثبيت %%p...
    pip install %%p --quiet
    if errorlevel 1 (
        echo ⚠️ فشل في تثبيت %%p
    ) else (
        echo ✅ تم تثبيت %%p
    )
)

echo.
echo 📦 تثبيت مكتبات Windows الاختيارية...
pip install pywin32 winshell --quiet >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبات Windows الاختيارية غير متوفرة (عادي)
) else (
    echo ✅ مكتبات Windows متوفرة
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                     🎨 إعداد الشعارات                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎨 إنشاء الشعارات...
if exist "create_logo.py" (
    python create_logo.py
    echo ✅ تم إنشاء الشعارات
) else (
    echo ⚠️ ملف إنشاء الشعارات غير موجود
)

REM تحويل PNG إلى ICO
if exist "youtube_downloader_icon.png" (
    echo 🔄 تحويل الشعار إلى ICO...
    python -c "
from PIL import Image
try:
    img = Image.open('youtube_downloader_icon.png')
    img.save('youtube_downloader.ico', format='ICO', sizes=[(16,16), (32,32), (48,48), (64,64)])
    print('✅ تم إنشاء youtube_downloader.ico')
except Exception as e:
    print(f'⚠️ فشل في إنشاء ICO: {e}')
"
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🔨 خيارات البناء                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo اختر نوع البناء:
echo.
echo 1. 📦 ملف exe واحد (مستحسن) - سهل التوزيع
echo 2. 📁 ملف exe مع مجلد - أسرع في التشغيل  
echo 3. 🔧 بناء متقدم - جميع الخيارات
echo 4. 💻 نسخة محمولة - تتطلب Python
echo 5. 🎯 جميع الأنواع
echo.

set /p BUILD_TYPE="اختر رقم (1-5): "

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                       🏗️ بدء البناء                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if "%BUILD_TYPE%"=="1" goto BUILD_ONEFILE
if "%BUILD_TYPE%"=="2" goto BUILD_ONEDIR
if "%BUILD_TYPE%"=="3" goto BUILD_ADVANCED
if "%BUILD_TYPE%"=="4" goto BUILD_PORTABLE
if "%BUILD_TYPE%"=="5" goto BUILD_ALL
goto BUILD_ONEFILE

:BUILD_ONEFILE
echo 📦 بناء ملف exe واحد...
echo ⏳ قد يستغرق 3-5 دقائق...
echo.

set ICON_OPTION=
if exist "youtube_downloader.ico" set ICON_OPTION=--icon "youtube_downloader.ico"

pyinstaller --onefile --windowed --name "YouTube_Video_Downloader" %ICON_OPTION% --add-data "youtube_downloader_icon.png;." --add-data "youtube_downloader_banner.png;." --add-data "youtube_icon_small.png;." --hidden-import "yt_dlp" --hidden-import "PIL" --hidden-import "PIL.Image" --hidden-import "PIL.ImageTk" --hidden-import "colorama" --hidden-import "tqdm" --clean gui_working.py

goto CHECK_RESULT

:BUILD_ONEDIR
echo 📁 بناء ملف exe مع مجلد...
echo ⏳ قد يستغرق 2-3 دقائق...
echo.

set ICON_OPTION=
if exist "youtube_downloader.ico" set ICON_OPTION=--icon "youtube_downloader.ico"

pyinstaller --windowed --name "YouTube_Video_Downloader" %ICON_OPTION% --add-data "youtube_downloader_icon.png;." --add-data "youtube_downloader_banner.png;." --add-data "youtube_icon_small.png;." --hidden-import "yt_dlp" --hidden-import "PIL" --hidden-import "PIL.Image" --hidden-import "PIL.ImageTk" --hidden-import "colorama" --hidden-import "tqdm" --clean gui_working.py

goto CHECK_RESULT

:BUILD_ADVANCED
echo 🔧 بناء متقدم...
echo ⏳ قد يستغرق 5-7 دقائق...
echo.

if exist "youtube_downloader_advanced.spec" (
    pyinstaller --clean youtube_downloader_advanced.spec
) else (
    echo ⚠️ ملف التكوين المتقدم غير موجود، استخدام البناء العادي...
    goto BUILD_ONEFILE
)

goto CHECK_RESULT

:BUILD_PORTABLE
echo 💻 إنشاء نسخة محمولة...
python build_exe.py
goto END

:BUILD_ALL
echo 🎯 بناء جميع الأنواع...
echo.

echo 1/3 - ملف exe واحد...
call :BUILD_ONEFILE

echo.
echo 2/3 - ملف exe مع مجلد...
call :BUILD_ONEDIR

echo.
echo 3/3 - نسخة محمولة...
python build_exe.py

goto CHECK_RESULT

:CHECK_RESULT
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      📊 نتائج البناء                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set SUCCESS=0

if exist "dist\YouTube_Video_Downloader.exe" (
    echo ✅ ملف exe واحد: dist\YouTube_Video_Downloader.exe
    
    for %%A in ("dist\YouTube_Video_Downloader.exe") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo    📊 الحجم: !size_mb! MB
    )
    
    set SUCCESS=1
)

if exist "dist\YouTube_Video_Downloader\YouTube_Video_Downloader.exe" (
    echo ✅ ملف exe مع مجلد: dist\YouTube_Video_Downloader\
    set SUCCESS=1
)

if exist "YouTube_Downloader_Portable" (
    echo ✅ نسخة محمولة: YouTube_Downloader_Portable\
    set SUCCESS=1
)

if %SUCCESS%==0 (
    echo ❌ فشل في البناء
    echo.
    echo 🔍 تحقق من:
    echo    - رسائل الخطأ أعلاه
    echo    - تثبيت جميع المكتبات
    echo    - مساحة القرص الصلب
    echo    - صلاحيات الكتابة
    goto END
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 تم البناء بنجاح!                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 💡 الخطوات التالية:
echo.
echo 1. 🚀 تشغيل البرنامج
echo 2. 🔗 إنشاء اختصار على سطح المكتب
echo 3. 📁 فتح مجلد الملفات
echo 4. 🧹 تنظيف الملفات المؤقتة
echo 5. ⏭️ تخطي
echo.

set /p NEXT_STEP="اختر رقم (1-5): "

if "%NEXT_STEP%"=="1" (
    echo 🚀 تشغيل البرنامج...
    if exist "dist\YouTube_Video_Downloader.exe" (
        start "" "dist\YouTube_Video_Downloader.exe"
    ) else if exist "dist\YouTube_Video_Downloader\YouTube_Video_Downloader.exe" (
        start "" "dist\YouTube_Video_Downloader\YouTube_Video_Downloader.exe"
    )
)

if "%NEXT_STEP%"=="2" (
    echo 🔗 إنشاء اختصار...
    python create_shortcut.py
)

if "%NEXT_STEP%"=="3" (
    echo 📁 فتح مجلد الملفات...
    start "" "dist"
)

if "%NEXT_STEP%"=="4" (
    echo 🧹 تنظيف الملفات المؤقتة...
    if exist "build" rmdir /s /q "build"
    if exist "__pycache__" rmdir /s /q "__pycache__"
    if exist "*.spec" del "*.spec"
    echo ✅ تم التنظيف
)

:END
echo.
echo 🙏 شكراً لاستخدام أداة بناء exe الشاملة!
echo.
echo 📋 ملخص ما تم إنجازه:
if exist "dist\YouTube_Video_Downloader.exe" echo    ✅ ملف exe جاهز للتوزيع
if exist "dist\YouTube_Video_Downloader\" echo    ✅ مجلد التطبيق جاهز
if exist "YouTube_Downloader_Portable" echo    ✅ نسخة محمولة جاهزة
echo.
echo 💡 يمكنك الآن مشاركة البرنامج مع الآخرين!
echo.
pause
