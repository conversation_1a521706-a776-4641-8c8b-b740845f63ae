#!/bin/bash
# YouTube Downloader - Linux/Mac Shell Launcher
# This script provides easy access to the YouTube Downloader on Linux/Mac

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "    YouTube Video Downloader"
echo "========================================"
echo -e "${NC}"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}ERROR: Python is not installed${NC}"
        echo "Please install Python 3.7 or higher"
        echo "Ubuntu/Debian: sudo apt install python3"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | grep -oP '\d+\.\d+')
MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f1)
MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 7 ]); then
    echo -e "${RED}ERROR: Python 3.7 or higher is required${NC}"
    echo "Current version: $($PYTHON_CMD --version)"
    exit 1
fi

echo -e "${GREEN}✅ Python version: $($PYTHON_CMD --version)${NC}"

# Check if launcher.py exists
if [ ! -f "launcher.py" ]; then
    echo -e "${RED}ERROR: launcher.py not found${NC}"
    echo "Please ensure all files are in the same directory"
    exit 1
fi

# Make the script executable if it isn't already
if [ ! -x "$0" ]; then
    chmod +x "$0"
fi

# Run the launcher
echo -e "${CYAN}Starting YouTube Downloader Launcher...${NC}"
echo

$PYTHON_CMD launcher.py

echo
echo -e "${YELLOW}YouTube Downloader has been closed.${NC}"
