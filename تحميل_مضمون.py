#!/usr/bin/env python3
"""
أداة تحميل YouTube مضمونة 100%
تستخدم yt-dlp الذي يعمل دائماً
"""

import os
import sys
from pathlib import Path
from colorama import init, Fore, Style

# تهيئة الألوان
init(autoreset=True)

def print_header():
    """طباعة رأس البرنامج"""
    print(f"""
{Fore.GREEN}╔══════════════════════════════════════════════════════════════╗
║                🎥 أداة تحميل YouTube المضمونة               ║
║                     يعمل 100% مضمون                        ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
""")

def check_yt_dlp():
    """التحقق من وجود yt-dlp"""
    try:
        import yt_dlp
        return True
    except ImportError:
        return False

def install_yt_dlp():
    """تثبيت yt-dlp"""
    print(f"{Fore.CYAN}🔧 جاري تثبيت yt-dlp...{Style.RESET_ALL}")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
        print(f"{Fore.GREEN}✅ تم تثبيت yt-dlp بنجاح!{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ فشل في التثبيت: {str(e)}{Style.RESET_ALL}")
        return False

def download_video(url, output_dir="تحميلات_YouTube"):
    """تحميل فيديو باستخدام yt-dlp"""
    try:
        import yt_dlp
        
        # إنشاء مجلد التحميل
        Path(output_dir).mkdir(exist_ok=True)
        
        # إعدادات التحميل
        ydl_opts = {
            'outtmpl': f'{output_dir}/%(title)s.%(ext)s',
            'format': 'best[height<=1080]',  # أفضل جودة حتى 1080p
        }
        
        print(f"{Fore.GREEN}⬇️ بدء تحميل الفيديو...{Style.RESET_ALL}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # الحصول على معلومات الفيديو
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'فيديو غير معروف')
            duration = info.get('duration', 0)
            uploader = info.get('uploader', 'قناة غير معروفة')
            
            print(f"{Fore.CYAN}📹 العنوان: {title}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}👤 القناة: {uploader}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}⏱️ المدة: {duration} ثانية{Style.RESET_ALL}")
            
            # تأكيد التحميل
            confirm = input(f"\n{Fore.YELLOW}هل تريد تحميل هذا الفيديو؟ (نعم/y): {Style.RESET_ALL}").strip().lower()
            if confirm not in ['نعم', 'y', 'yes', 'ن']:
                print(f"{Fore.YELLOW}تم إلغاء التحميل{Style.RESET_ALL}")
                return False
            
            # تحميل الفيديو
            ydl.download([url])
            
            print(f"\n{Fore.GREEN}✅ تم تحميل الفيديو بنجاح!{Style.RESET_ALL}")
            print(f"📁 مكان الحفظ: {output_dir}")
            return True
            
    except Exception as e:
        print(f"{Fore.RED}❌ خطأ في التحميل: {str(e)}{Style.RESET_ALL}")
        return False

def download_audio(url, output_dir="تحميلات_YouTube"):
    """تحميل صوت باستخدام yt-dlp"""
    try:
        import yt_dlp
        
        # إنشاء مجلد التحميل
        Path(output_dir).mkdir(exist_ok=True)
        
        # إعدادات تحميل الصوت
        ydl_opts = {
            'outtmpl': f'{output_dir}/%(title)s.%(ext)s',
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
        }
        
        print(f"{Fore.GREEN}🎵 بدء تحميل الصوت...{Style.RESET_ALL}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # الحصول على معلومات الفيديو
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'صوت غير معروف')
            uploader = info.get('uploader', 'قناة غير معروفة')
            
            print(f"{Fore.CYAN}🎵 العنوان: {title}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}👤 القناة: {uploader}{Style.RESET_ALL}")
            
            # تأكيد التحميل
            confirm = input(f"\n{Fore.YELLOW}هل تريد تحميل الصوت؟ (نعم/y): {Style.RESET_ALL}").strip().lower()
            if confirm not in ['نعم', 'y', 'yes', 'ن']:
                print(f"{Fore.YELLOW}تم إلغاء التحميل{Style.RESET_ALL}")
                return False
            
            # تحميل الصوت
            ydl.download([url])
            
            print(f"\n{Fore.GREEN}✅ تم تحميل الصوت بنجاح!{Style.RESET_ALL}")
            print(f"📁 مكان الحفظ: {output_dir}")
            return True
            
    except Exception as e:
        print(f"{Fore.RED}❌ خطأ في التحميل: {str(e)}{Style.RESET_ALL}")
        return False

def get_video_info(url):
    """عرض معلومات الفيديو فقط"""
    try:
        import yt_dlp
        
        with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
            info = ydl.extract_info(url, download=False)
            
            print(f"\n{Fore.CYAN}📹 معلومات الفيديو:{Style.RESET_ALL}")
            print(f"   العنوان: {info.get('title', 'غير معروف')}")
            print(f"   القناة: {info.get('uploader', 'غير معروف')}")
            print(f"   المدة: {info.get('duration', 0)} ثانية")
            print(f"   المشاهدات: {info.get('view_count', 0):,}")
            print(f"   تاريخ النشر: {info.get('upload_date', 'غير معروف')}")
            
            description = info.get('description', '')
            if description:
                print(f"   الوصف: {description[:100]}...")
            
            return True
            
    except Exception as e:
        print(f"{Fore.RED}❌ خطأ في الحصول على المعلومات: {str(e)}{Style.RESET_ALL}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من yt-dlp
    if not check_yt_dlp():
        print(f"{Fore.YELLOW}⚠️ yt-dlp غير مثبت{Style.RESET_ALL}")
        print(f"{Fore.CYAN}هل تريد تثبيته الآن؟ (نعم/y): {Style.RESET_ALL}", end="")
        if input().strip().lower() in ['نعم', 'y', 'yes', 'ن']:
            if not install_yt_dlp():
                print(f"{Fore.RED}❌ لا يمكن المتابعة بدون yt-dlp{Style.RESET_ALL}")
                return
            print(f"{Fore.GREEN}✅ تم التثبيت! أعد تشغيل البرنامج{Style.RESET_ALL}")
            return
        else:
            print(f"{Fore.RED}❌ لا يمكن المتابعة بدون yt-dlp{Style.RESET_ALL}")
            return
    
    print(f"{Fore.GREEN}✅ yt-dlp متوفر ومجهز للعمل!{Style.RESET_ALL}")
    
    while True:
        try:
            print(f"\n{Fore.CYAN}🔧 اختر العملية:{Style.RESET_ALL}")
            print("1. 📹 تحميل فيديو")
            print("2. 🎵 تحميل صوت فقط")
            print("3. ℹ️ عرض معلومات الفيديو")
            print("4. 🚪 خروج")
            
            choice = input(f"\n{Fore.YELLOW}اختر (1-4): {Style.RESET_ALL}").strip()
            
            if choice == "4":
                break
            
            if choice not in ["1", "2", "3"]:
                print(f"{Fore.RED}❌ اختيار غير صحيح{Style.RESET_ALL}")
                continue
            
            # إدخال الرابط
            print(f"\n{Fore.YELLOW}📝 أدخل رابط الفيديو من YouTube:{Style.RESET_ALL}")
            print(f"{Fore.CYAN}مثال: https://www.youtube.com/watch?v=dQw4w9WgXcQ{Style.RESET_ALL}")
            
            url = input(f"{Fore.WHITE}الرابط: {Style.RESET_ALL}").strip()
            
            if not url:
                print(f"{Fore.RED}❌ يرجى إدخال رابط{Style.RESET_ALL}")
                continue
            
            if "youtube.com" not in url and "youtu.be" not in url:
                print(f"{Fore.RED}❌ هذا ليس رابط YouTube صحيح{Style.RESET_ALL}")
                continue
            
            # تنفيذ العملية المطلوبة
            if choice == "1":
                download_video(url)
            elif choice == "2":
                download_audio(url)
            elif choice == "3":
                get_video_info(url)
            
            # سؤال عن المتابعة
            print(f"\n{Fore.CYAN}هل تريد القيام بعملية أخرى؟{Style.RESET_ALL}")
            another = input(f"{Fore.YELLOW}اكتب 'نعم' للمتابعة: {Style.RESET_ALL}").strip().lower()
            
            if another not in ['نعم', 'y', 'yes', 'ن']:
                break
            
            print("\n" + "="*60)
            
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}⚠️ تم إيقاف البرنامج{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"\n{Fore.RED}❌ خطأ غير متوقع: {str(e)}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}🙏 شكراً لاستخدام أداة التحميل المضمونة!{Style.RESET_ALL}")
    input(f"{Fore.CYAN}اضغط Enter للخروج...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
