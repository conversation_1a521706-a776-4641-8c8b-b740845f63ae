#!/usr/bin/env python3
"""
بناء ملف exe لأداة تحميل YouTube
تحويل البرنامج إلى ملف تنفيذي لسطح المكتب
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # التحقق من الملفات المطلوبة
    required_files = [
        'gui_working.py',
        'gui.py', 
        'downloader.py',
        'تحميل_مضمون.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PyInstaller: {e}")
        return False

def install_dependencies():
    """تثبيت جميع المكتبات المطلوبة"""
    print("📦 تثبيت المكتبات المطلوبة...")
    
    dependencies = [
        "yt-dlp",
        "Pillow", 
        "colorama",
        "tqdm",
        "pyinstaller"
    ]
    
    for dep in dependencies:
        try:
            print(f"   تثبيت {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️ فشل في تثبيت {dep}")
        except Exception as e:
            print(f"⚠️ فشل في تثبيت {dep}: {e}")
    
    print("✅ تم تثبيت المكتبات")

def create_icon():
    """إنشاء أيقونة ICO للملف التنفيذي"""
    print("🎨 إنشاء أيقونة ICO...")
    
    try:
        # إنشاء الشعارات أولاً
        if Path("create_logo.py").exists():
            subprocess.run([sys.executable, "create_logo.py"], capture_output=True)
        
        # تحويل PNG إلى ICO
        if Path("youtube_downloader_icon.png").exists():
            from PIL import Image
            
            # فتح الصورة
            img = Image.open("youtube_downloader_icon.png")
            
            # إنشاء أحجام متعددة للأيقونة
            icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
            icon_images = []
            
            for size in icon_sizes:
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                icon_images.append(resized_img)
            
            # حفظ كملف ICO
            icon_images[0].save("youtube_downloader.ico", format="ICO", 
                              sizes=icon_sizes, append_images=icon_images[1:])
            
            print("✅ تم إنشاء youtube_downloader.ico")
            return "youtube_downloader.ico"
        
    except Exception as e:
        print(f"⚠️ فشل في إنشاء الأيقونة: {e}")
    
    return None

def create_spec_file():
    """إنشاء ملف spec مخصص"""
    print("📝 إنشاء ملف التكوين...")
    
    icon_path = create_icon()
    icon_option = f"icon='{icon_path}'," if icon_path else ""
    
    spec_content = f"""
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# البيانات الإضافية (الشعارات والملفات)
added_files = [
    ('youtube_downloader_icon.png', '.'),
    ('youtube_downloader_banner.png', '.'),
    ('youtube_icon_small.png', '.'),
    ('ascii_logo.py', '.'),
]

# إضافة الملفات إذا كانت موجودة
import os
for file_path, dest in added_files[:]:
    if not os.path.exists(file_path):
        added_files.remove((file_path, dest))

a = Analysis(
    ['gui_working.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'yt_dlp',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'colorama',
        'tqdm',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YouTube_Video_Downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {icon_option}
)
"""
    
    with open("youtube_downloader.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء youtube_downloader.spec")
    return "youtube_downloader.spec"

def build_exe():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # إنشاء ملف spec
        spec_file = create_spec_file()
        
        # بناء الملف التنفيذي
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
        
        print("⏳ جاري البناء... (قد يستغرق عدة دقائق)")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            
            # التحقق من وجود الملف
            exe_path = Path("dist/YouTube_Video_Downloader.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 الملف: {exe_path}")
                print(f"📊 الحجم: {size_mb:.1f} MB")
                return str(exe_path)
            else:
                print("❌ لم يتم العثور على الملف التنفيذي")
                return None
        else:
            print("❌ فشل في البناء:")
            print(result.stderr)
            return None
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return None

def create_simple_exe():
    """إنشاء ملف exe بسيط"""
    print("🔨 إنشاء ملف exe بسيط...")
    
    try:
        icon_path = create_icon()
        icon_option = ["--icon", icon_path] if icon_path else []
        
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name", "YouTube_Video_Downloader",
            "--add-data", "youtube_downloader_icon.png;.",
            "--add-data", "youtube_downloader_banner.png;.",
            "--add-data", "youtube_icon_small.png;.",
            "--hidden-import", "yt_dlp",
            "--hidden-import", "PIL",
            "--hidden-import", "PIL.Image", 
            "--hidden-import", "PIL.ImageTk",
            "--hidden-import", "colorama",
            "--hidden-import", "tqdm"
        ] + icon_option + ["gui_working.py"]
        
        print("⏳ جاري البناء...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إنشاء الملف التنفيذي!")
            exe_path = Path("dist/YouTube_Video_Downloader.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 الملف: {exe_path}")
                print(f"📊 الحجم: {size_mb:.1f} MB")
                return str(exe_path)
        else:
            print("❌ فشل في البناء:")
            print(result.stderr[:500])
            return None
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def create_portable_version():
    """إنشاء نسخة محمولة"""
    print("📦 إنشاء نسخة محمولة...")
    
    try:
        # إنشاء مجلد النسخة المحمولة
        portable_dir = Path("YouTube_Downloader_Portable")
        if portable_dir.exists():
            shutil.rmtree(portable_dir)
        portable_dir.mkdir()
        
        # نسخ الملفات المطلوبة
        files_to_copy = [
            "gui_working.py",
            "gui.py",
            "downloader.py", 
            "تحميل_مضمون.py",
            "ascii_logo.py",
            "create_logo.py",
            "youtube_downloader_icon.png",
            "youtube_downloader_banner.png",
            "youtube_icon_small.png"
        ]
        
        for file in files_to_copy:
            if Path(file).exists():
                shutil.copy2(file, portable_dir)
        
        # إنشاء ملف تشغيل
        run_script = """@echo off
title YouTube Video Downloader - Portable
echo Starting YouTube Video Downloader...
python gui_working.py
pause"""
        
        with open(portable_dir / "run.bat", "w") as f:
            f.write(run_script)
        
        # إنشاء ملف README
        readme_content = """# YouTube Video Downloader - Portable Version

## How to use:
1. Make sure Python is installed on your system
2. Double-click 'run.bat' to start the application
3. Or run: python gui_working.py

## Requirements:
- Python 3.7+
- Internet connection

## First time setup:
Run this command to install dependencies:
pip install yt-dlp Pillow colorama tqdm

Enjoy downloading!
"""
        
        with open(portable_dir / "README.txt", "w") as f:
            f.write(readme_content)
        
        print(f"✅ تم إنشاء النسخة المحمولة: {portable_dir}")
        return str(portable_dir)
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة المحمولة: {e}")
        return None

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_dirs = ["build", "__pycache__"]
    temp_files = ["youtube_downloader.spec"]
    
    for dir_name in temp_dirs:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"   حذف {dir_name}/")
    
    for file_name in temp_files:
        if Path(file_name).exists():
            Path(file_name).unlink()
            print(f"   حذف {file_name}")

def main():
    """الدالة الرئيسية"""
    print("🏗️ بناء ملف exe لأداة تحميل YouTube")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return
    
    # تثبيت المكتبات
    install_dependencies()
    
    print("\n🔧 اختر نوع البناء:")
    print("1. ملف exe واحد (مستحسن)")
    print("2. ملف exe مع ملفات منفصلة")
    print("3. نسخة محمولة (Python مطلوب)")
    print("4. جميع الأنواع")
    
    choice = input("\nاختر (1-4): ").strip()
    
    success = False
    
    if choice in ["1", "4"]:
        print("\n" + "="*60)
        exe_path = create_simple_exe()
        if exe_path:
            success = True
            print(f"✅ ملف exe جاهز: {exe_path}")
    
    if choice in ["2", "4"]:
        print("\n" + "="*60)
        exe_path = build_exe()
        if exe_path:
            success = True
            print(f"✅ ملف exe مع ملفات منفصلة: {exe_path}")
    
    if choice in ["3", "4"]:
        print("\n" + "="*60)
        portable_path = create_portable_version()
        if portable_path:
            success = True
            print(f"✅ النسخة المحمولة: {portable_path}")
    
    # تنظيف
    cleanup()
    
    if success:
        print("\n🎉 تم بناء البرنامج بنجاح!")
        print("\n📋 الملفات المنشأة:")
        
        if Path("dist/YouTube_Video_Downloader.exe").exists():
            print("   📁 dist/YouTube_Video_Downloader.exe")
        
        if Path("YouTube_Downloader_Portable").exists():
            print("   📁 YouTube_Downloader_Portable/")
        
        print("\n💡 يمكنك الآن:")
        print("   - تشغيل الملف التنفيذي مباشرة")
        print("   - نسخه إلى أي مكان")
        print("   - مشاركته مع الآخرين")
        print("   - إنشاء اختصار على سطح المكتب")
        
    else:
        print("\n❌ فشل في البناء")
        print("تأكد من:")
        print("   - تثبيت Python بشكل صحيح")
        print("   - وجود جميع الملفات المطلوبة")
        print("   - اتصال الإنترنت لتحميل المكتبات")

if __name__ == "__main__":
    main()
