#!/usr/bin/env python3
"""
اختبار بسيط للتحميل من YouTube
للتأكد من أن yt-dlp يعمل بشكل صحيح
"""

import os
import sys
from pathlib import Path

def test_yt_dlp():
    """اختبار yt-dlp"""
    try:
        import yt_dlp
        print("✅ yt-dlp متوفر")
        print(f"الإصدار: {yt_dlp.version.__version__}")
        return True
    except ImportError:
        print("❌ yt-dlp غير مثبت")
        return False

def test_download(url, output_dir="test_downloads"):
    """اختبار تحميل فيديو"""
    if not test_yt_dlp():
        print("يرجى تثبيت yt-dlp أولاً: pip install yt-dlp")
        return False
    
    import yt_dlp
    
    # إنشاء مجلد الاختبار
    Path(output_dir).mkdir(exist_ok=True)
    
    # إعدادات التحميل
    ydl_opts = {
        'outtmpl': f'{output_dir}/%(title)s.%(ext)s',
        'format': 'worst',  # أسوأ جودة للاختبار السريع
    }
    
    try:
        print(f"🔍 اختبار الرابط: {url}")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # الحصول على معلومات الفيديو أولاً
            print("📋 جاري الحصول على معلومات الفيديو...")
            info = ydl.extract_info(url, download=False)
            
            title = info.get('title', 'غير معروف')
            duration = info.get('duration', 0)
            uploader = info.get('uploader', 'غير معروف')
            
            print(f"📹 العنوان: {title}")
            print(f"👤 القناة: {uploader}")
            print(f"⏱️ المدة: {duration} ثانية")
            
            # تأكيد التحميل
            confirm = input("\nهل تريد تحميل هذا الفيديو؟ (y/n): ").strip().lower()
            if confirm not in ['y', 'yes', 'نعم', 'ن']:
                print("تم إلغاء التحميل")
                return False
            
            # بدء التحميل
            print("⬇️ بدء التحميل...")
            ydl.download([url])
            
            print("✅ تم التحميل بنجاح!")
            print(f"📁 مكان الحفظ: {output_dir}")
            
            # عرض الملفات المحملة
            files = list(Path(output_dir).glob("*"))
            if files:
                print("\n📄 الملفات المحملة:")
                for file in files:
                    print(f"  - {file.name}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحميل: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار تحميل YouTube")
    print("=" * 40)
    
    # اختبار yt-dlp
    if not test_yt_dlp():
        install = input("هل تريد تثبيت yt-dlp؟ (y/n): ").strip().lower()
        if install in ['y', 'yes', 'نعم', 'ن']:
            try:
                import subprocess
                print("📦 جاري تثبيت yt-dlp...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
                print("✅ تم تثبيت yt-dlp بنجاح!")
                print("أعد تشغيل البرنامج")
                return
            except Exception as e:
                print(f"❌ فشل التثبيت: {str(e)}")
                return
        else:
            print("لا يمكن المتابعة بدون yt-dlp")
            return
    
    # إدخال الرابط
    print("\n📝 أدخل رابط فيديو YouTube للاختبار:")
    print("مثال: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    
    url = input("الرابط: ").strip()
    
    if not url:
        print("❌ لم يتم إدخال رابط")
        return
    
    if "youtube.com" not in url and "youtu.be" not in url:
        print("❌ هذا ليس رابط YouTube صحيح")
        return
    
    # اختبار التحميل
    success = test_download(url)
    
    if success:
        print("\n🎉 الاختبار نجح! yt-dlp يعمل بشكل صحيح")
        print("يمكنك الآن استخدام الواجهة الرسومية بثقة")
    else:
        print("\n❌ الاختبار فشل. هناك مشكلة في الإعداد")

if __name__ == "__main__":
    main()
