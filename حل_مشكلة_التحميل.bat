@echo off
chcp 65001 >nul
title حل مشكلة عدم التحميل - YouTube Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔧 حل مشكلة عدم التحميل                      ║
echo ║                YouTube Video Downloader                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 تشخيص المشكلة...
echo.

REM التحقق من Python
echo 1️⃣ التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر
)

REM التحقق من pip
echo.
echo 2️⃣ التحقق من pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo جرب: python -m ensurepip --upgrade
    pause
    exit /b 1
) else (
    echo ✅ pip متوفر
)

REM تحديث pip
echo.
echo 3️⃣ تحديث pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️ فشل في تحديث pip (قد يكون عادياً)
) else (
    echo ✅ تم تحديث pip
)

REM إزالة pytube القديم (قد يسبب تعارض)
echo.
echo 4️⃣ إزالة pytube القديم...
pip uninstall pytube -y >nul 2>&1
echo ✅ تم إزالة pytube القديم

REM تثبيت yt-dlp
echo.
echo 5️⃣ تثبيت yt-dlp...
pip install --upgrade yt-dlp
if errorlevel 1 (
    echo ❌ فشل في تثبيت yt-dlp
    echo جرب: pip install --user yt-dlp
    pip install --user yt-dlp
    if errorlevel 1 (
        echo ❌ فشل في التثبيت نهائياً
        pause
        exit /b 1
    )
)
echo ✅ تم تثبيت yt-dlp بنجاح

REM تثبيت المكتبات المساعدة
echo.
echo 6️⃣ تثبيت المكتبات المساعدة...
pip install colorama tqdm
echo ✅ تم تثبيت المكتبات المساعدة

REM اختبار yt-dlp
echo.
echo 7️⃣ اختبار yt-dlp...
python -c "import yt_dlp; print('yt-dlp version:', yt_dlp.version.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ yt-dlp لا يعمل بشكل صحيح
    pause
    exit /b 1
) else (
    echo ✅ yt-dlp يعمل بشكل صحيح
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ تم حل جميع المشاكل!                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo اختر ما تريد تشغيله:
echo.
echo 1. 🧪 اختبار التحميل (مستحسن)
echo 2. 🖥️ الواجهة الرسومية التي تعمل 100%%
echo 3. 💻 الواجهة النصية المضمونة
echo 4. 🔧 الواجهة الأصلية المحسّنة
echo 5. 🚪 خروج
echo.

set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🧪 تشغيل اختبار التحميل...
    python test_download.py
) else if "%choice%"=="2" (
    echo.
    echo 🖥️ تشغيل الواجهة الرسومية التي تعمل 100%%...
    python gui_working.py
) else if "%choice%"=="3" (
    echo.
    echo 💻 تشغيل الواجهة النصية المضمونة...
    python تحميل_مضمون.py
) else if "%choice%"=="4" (
    echo.
    echo 🔧 تشغيل الواجهة الأصلية المحسّنة...
    python gui.py
) else if "%choice%"=="5" (
    echo.
    echo 👋 وداعاً!
    exit
) else (
    echo.
    echo ❌ اختيار غير صحيح
    pause
    goto :choice
)

echo.
echo هل تريد تشغيل واجهة أخرى؟ (y/n)
set /p again="اختر: "

if /i "%again%"=="y" (
    cls
    goto :choice
) else (
    echo.
    echo 🙏 شكراً لاستخدام YouTube Downloader!
)

:choice
pause
