#!/usr/bin/env python3
"""
واجهة رسومية محسّنة لتحميل فيديوهات YouTube
حل مشكلة إدخال الروابط مع واجهة سهلة ومحسّنة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import webbrowser
from pathlib import Path

# محاولة استيراد المكتبات
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False

try:
    from downloader import YouTubeDownloader
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False


class FixedYouTubeGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎥 أداة تحميل YouTube - واجهة محسّنة")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # تعيين الخط العربي
        try:
            self.arabic_font = ("Arial Unicode MS", 10)
            self.arabic_font_bold = ("Arial Unicode MS", 12, "bold")
            self.arabic_font_large = ("Arial Unicode MS", 14, "bold")
        except:
            self.arabic_font = ("Arial", 10)
            self.arabic_font_bold = ("Arial", 12, "bold")
            self.arabic_font_large = ("Arial", 14, "bold")
        
        # المتغيرات
        self.url_var = tk.StringVar()
        self.download_type_var = tk.StringVar(value="video")
        self.quality_var = tk.StringVar(value="best")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="جاهز للتحميل - الصق الرابط أدناه")
        
        # مجلد التحميل
        self.download_path = "تحميلات_YouTube"
        Path(self.download_path).mkdir(exist_ok=True)
        
        # إعداد yt-dlp
        self.setup_yt_dlp()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # ربط الأحداث
        self.bind_events()
    
    def setup_yt_dlp(self):
        """إعداد yt-dlp"""
        if YT_DLP_AVAILABLE:
            self.ydl_opts_video = {
                'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
                'format': 'best[height<=1080]',
                'noplaylist': True,
            }
            
            self.ydl_opts_audio = {
                'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
                'format': 'bestaudio/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'noplaylist': True,
            }
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان الرئيسي
        title_frame = tk.Frame(main_frame, bg="#2E86AB", height=60)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🎥 أداة تحميل فيديوهات YouTube", 
                              font=self.arabic_font_large, fg="white", bg="#2E86AB")
        title_label.pack(expand=True)
        
        # قسم إدخال الرابط - محسّن
        url_section = self.create_url_section(main_frame)
        url_section.pack(fill=tk.X, pady=(0, 15))
        
        # قسم الخيارات
        options_section = self.create_options_section(main_frame)
        options_section.pack(fill=tk.X, pady=(0, 15))
        
        # قسم مجلد الحفظ
        folder_section = self.create_folder_section(main_frame)
        folder_section.pack(fill=tk.X, pady=(0, 15))
        
        # قسم التقدم
        progress_section = self.create_progress_section(main_frame)
        progress_section.pack(fill=tk.X, pady=(0, 15))
        
        # أزرار التحكم
        buttons_section = self.create_buttons_section(main_frame)
        buttons_section.pack(fill=tk.X, pady=(0, 10))
        
        # قسم المساعدة
        help_section = self.create_help_section(main_frame)
        help_section.pack(fill=tk.X)
    
    def create_url_section(self, parent):
        """إنشاء قسم إدخال الرابط"""
        frame = tk.LabelFrame(parent, text="📝 رابط الفيديو", 
                             font=self.arabic_font_bold, padx=15, pady=15)
        
        # تعليمات واضحة
        instruction_label = tk.Label(frame, 
                                   text="الصق رابط الفيديو من YouTube في الحقل أدناه:",
                                   font=self.arabic_font, fg="#2E86AB")
        instruction_label.pack(anchor=tk.W, pady=(0, 10))
        
        # إطار حقل الإدخال
        entry_frame = tk.Frame(frame, bg="#F0F8FF", relief=tk.SUNKEN, bd=2)
        entry_frame.pack(fill=tk.X, pady=(0, 10))
        
        # حقل إدخال الرابط - كبير ومحسّن
        self.url_entry = tk.Entry(entry_frame, textvariable=self.url_var, 
                                 font=("Consolas", 11), width=70, 
                                 relief=tk.FLAT, bd=5, bg="#F0F8FF")
        self.url_entry.pack(fill=tk.X, padx=5, pady=5)
        
        # أزرار مساعدة
        buttons_frame = tk.Frame(frame)
        buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        # زر لصق
        paste_btn = tk.Button(buttons_frame, text="📋 لصق من الحافظة", 
                             command=self.paste_url, bg="#4CAF50", fg="white",
                             font=self.arabic_font, relief=tk.RAISED, bd=2)
        paste_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر مسح
        clear_btn = tk.Button(buttons_frame, text="🗑️ مسح الحقل", 
                             command=self.clear_url, bg="#FF6B6B", fg="white",
                             font=self.arabic_font, relief=tk.RAISED, bd=2)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر معلومات
        info_btn = tk.Button(buttons_frame, text="ℹ️ معلومات الفيديو", 
                            command=self.show_video_info, bg="#17A2B8", fg="white",
                            font=self.arabic_font, relief=tk.RAISED, bd=2)
        info_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر فتح YouTube
        youtube_btn = tk.Button(buttons_frame, text="🌐 فتح YouTube", 
                               command=self.open_youtube, bg="#FF0000", fg="white",
                               font=self.arabic_font, relief=tk.RAISED, bd=2)
        youtube_btn.pack(side=tk.RIGHT)
        
        return frame
    
    def create_options_section(self, parent):
        """إنشاء قسم الخيارات"""
        frame = tk.LabelFrame(parent, text="⚙️ خيارات التحميل", 
                             font=self.arabic_font_bold, padx=15, pady=15)
        
        # نوع التحميل
        type_frame = tk.Frame(frame)
        type_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(type_frame, text="نوع التحميل:", 
                font=self.arabic_font_bold).pack(anchor=tk.W, pady=(0, 5))
        
        radio_frame = tk.Frame(type_frame)
        radio_frame.pack(anchor=tk.W)
        
        tk.Radiobutton(radio_frame, text="🎥 فيديو كامل", 
                      variable=self.download_type_var, value="video",
                      font=self.arabic_font, command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Radiobutton(radio_frame, text="🎵 صوت فقط (MP3)", 
                      variable=self.download_type_var, value="audio",
                      font=self.arabic_font, command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Radiobutton(radio_frame, text="📋 قائمة تشغيل", 
                      variable=self.download_type_var, value="playlist",
                      font=self.arabic_font, command=self.on_type_change).pack(side=tk.LEFT)
        
        # جودة الفيديو
        quality_frame = tk.Frame(frame)
        quality_frame.pack(fill=tk.X)
        
        tk.Label(quality_frame, text="جودة الفيديو:", 
                font=self.arabic_font_bold).pack(anchor=tk.W, pady=(0, 5))
        
        self.quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var,
                                         values=["best", "1080p", "720p", "480p", "360p", "worst"],
                                         state="readonly", font=self.arabic_font, width=20)
        self.quality_combo.pack(anchor=tk.W)
        
        return frame
    
    def create_folder_section(self, parent):
        """إنشاء قسم مجلد الحفظ"""
        frame = tk.LabelFrame(parent, text="📁 مجلد الحفظ", 
                             font=self.arabic_font_bold, padx=15, pady=15)
        
        folder_frame = tk.Frame(frame)
        folder_frame.pack(fill=tk.X)
        
        self.folder_label = tk.Label(folder_frame, text=f"📂 {self.download_path}", 
                                    font=self.arabic_font, anchor=tk.W, bg="#E8F5E8")
        self.folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(folder_frame, text="📂 تغيير المجلد", 
                              command=self.browse_folder, bg="#FFC107", 
                              font=self.arabic_font, relief=tk.RAISED, bd=2)
        browse_btn.pack(side=tk.RIGHT)
        
        return frame
    
    def create_progress_section(self, parent):
        """إنشاء قسم التقدم"""
        frame = tk.LabelFrame(parent, text="📊 حالة التحميل", 
                             font=self.arabic_font_bold, padx=15, pady=15)
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(frame, variable=self.progress_var, 
                                           maximum=100, length=500, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # نص الحالة
        self.status_label = tk.Label(frame, textvariable=self.status_var, 
                                    font=self.arabic_font, fg="#2E86AB")
        self.status_label.pack(anchor=tk.W)
        
        return frame
    
    def create_buttons_section(self, parent):
        """إنشاء قسم الأزرار"""
        frame = tk.Frame(parent)
        
        # زر التحميل الرئيسي
        self.download_btn = tk.Button(frame, text="⬇️ بدء التحميل", 
                                     command=self.start_download,
                                     font=("Arial", 14, "bold"), 
                                     bg="#28A745", fg="white", 
                                     height=2, width=20, relief=tk.RAISED, bd=3)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        # زر إيقاف
        self.stop_btn = tk.Button(frame, text="⏹️ إيقاف", 
                                 command=self.stop_download,
                                 font=self.arabic_font_bold, 
                                 bg="#DC3545", fg="white", 
                                 height=2, width=15, relief=tk.RAISED, bd=3,
                                 state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        # زر فتح المجلد
        open_folder_btn = tk.Button(frame, text="📂 فتح مجلد التحميل", 
                                   command=self.open_download_folder,
                                   font=self.arabic_font_bold, 
                                   bg="#6C757D", fg="white", 
                                   height=2, width=18, relief=tk.RAISED, bd=3)
        open_folder_btn.pack(side=tk.RIGHT)
        
        return frame
    
    def create_help_section(self, parent):
        """إنشاء قسم المساعدة"""
        frame = tk.LabelFrame(parent, text="💡 مساعدة", 
                             font=self.arabic_font_bold, padx=15, pady=10)
        
        help_text = """
🔗 كيفية الاستخدام:
1. انسخ رابط الفيديو من YouTube
2. الصقه في الحقل أعلاه أو اضغط "لصق من الحافظة"
3. اختر نوع التحميل (فيديو أو صوت)
4. اختر الجودة المطلوبة
5. اضغط "بدء التحميل"

💡 نصائح:
• يمكنك الضغط على Enter بعد لصق الرابط لبدء التحميل
• استخدم "معلومات الفيديو" لمعاينة الفيديو قبل التحميل
• الملفات تُحفظ في مجلد "تحميلات_YouTube" افتراضياً
        """
        
        help_label = tk.Label(frame, text=help_text, font=("Arial", 9), 
                             justify=tk.RIGHT, anchor=tk.NE, fg="#495057")
        help_label.pack(fill=tk.BOTH)
        
        return frame
    
    def bind_events(self):
        """ربط الأحداث"""
        # ربط Enter بزر التحميل
        self.url_entry.bind('<Return>', lambda e: self.start_download())
        self.url_entry.bind('<Control-v>', lambda e: self.root.after(10, self.paste_url))
        
        # تركيز على حقل الإدخال عند بدء البرنامج
        self.root.after(100, lambda: self.url_entry.focus())
        
        # تحديث الحالة عند تغيير الرابط
        self.url_var.trace('w', self.on_url_change)
    
    def on_url_change(self, *args):
        """عند تغيير الرابط"""
        url = self.url_var.get().strip()
        if url:
            if "youtube.com" in url or "youtu.be" in url:
                self.status_var.set("✅ رابط صحيح - جاهز للتحميل")
                self.download_btn.config(state=tk.NORMAL)
            else:
                self.status_var.set("⚠️ هذا ليس رابط YouTube صحيح")
                self.download_btn.config(state=tk.DISABLED)
        else:
            self.status_var.set("📝 أدخل رابط الفيديو للمتابعة")
            self.download_btn.config(state=tk.DISABLED)
    
    def on_type_change(self):
        """عند تغيير نوع التحميل"""
        download_type = self.download_type_var.get()
        if download_type == "audio":
            self.quality_combo.config(state=tk.DISABLED)
        else:
            self.quality_combo.config(state="readonly")
    
    def paste_url(self):
        """لصق الرابط من الحافظة"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.url_var.set(clipboard_content.strip())
                self.status_var.set("✅ تم لصق الرابط من الحافظة")
            else:
                messagebox.showwarning("تحذير", "الحافظة فارغة")
        except tk.TclError:
            messagebox.showwarning("تحذير", "لا يمكن الوصول للحافظة")
    
    def clear_url(self):
        """مسح حقل الرابط"""
        self.url_var.set("")
        self.status_var.set("تم مسح الرابط")
        self.url_entry.focus()
        self.progress_var.set(0)
    
    def browse_folder(self):
        """اختيار مجلد الحفظ"""
        folder = filedialog.askdirectory(initialdir=self.download_path,
                                        title="اختر مجلد الحفظ")
        if folder:
            self.download_path = folder
            self.folder_label.config(text=f"📂 {self.download_path}")
            self.status_var.set("✅ تم تغيير مجلد الحفظ")
            # تحديث إعدادات yt-dlp
            self.setup_yt_dlp()
    
    def open_youtube(self):
        """فتح موقع YouTube"""
        webbrowser.open("https://www.youtube.com")
    
    def open_download_folder(self):
        """فتح مجلد التحميل"""
        try:
            import os
            import platform
            
            if platform.system() == "Windows":
                os.startfile(self.download_path)
            elif platform.system() == "Darwin":  # macOS
                os.system(f"open '{self.download_path}'")
            else:  # Linux
                os.system(f"xdg-open '{self.download_path}'")
        except Exception as e:
            messagebox.showerror("خطأ", f"لا يمكن فتح المجلد: {str(e)}")
    
    def show_video_info(self):
        """عرض معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الفيديو أولاً")
            return
        
        if not YT_DLP_AVAILABLE:
            messagebox.showerror("خطأ", "yt-dlp غير متوفر. قم بتثبيته: pip install yt-dlp")
            return
        
        def get_info():
            try:
                self.status_var.set("🔍 جاري الحصول على معلومات الفيديو...")
                
                with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                    info = ydl.extract_info(url, download=False)
                    
                    info_text = f"""
📹 العنوان: {info.get('title', 'غير معروف')}
👤 القناة: {info.get('uploader', 'غير معروف')}
⏱️ المدة: {info.get('duration', 0)} ثانية
👁️ المشاهدات: {info.get('view_count', 0):,}
📅 تاريخ النشر: {info.get('upload_date', 'غير معروف')}
🆔 معرف الفيديو: {info.get('id', 'غير معروف')}

📝 الوصف:
{info.get('description', 'لا يوجد وصف')[:200]}...
                    """
                    
                    # إنشاء نافذة منفصلة للمعلومات
                    info_window = tk.Toplevel(self.root)
                    info_window.title("معلومات الفيديو")
                    info_window.geometry("500x400")
                    info_window.resizable(True, True)
                    
                    # إضافة النص
                    text_widget = tk.Text(info_window, wrap=tk.WORD, font=self.arabic_font)
                    text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                    text_widget.insert(tk.END, info_text)
                    text_widget.config(state=tk.DISABLED)
                    
                    # شريط التمرير
                    scrollbar = ttk.Scrollbar(text_widget)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                    text_widget.config(yscrollcommand=scrollbar.set)
                    scrollbar.config(command=text_widget.yview)
                    
                    self.status_var.set("✅ تم الحصول على معلومات الفيديو")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في الحصول على معلومات الفيديو:\n{str(e)}")
                self.status_var.set("❌ خطأ في الحصول على المعلومات")
        
        threading.Thread(target=get_info, daemon=True).start()
    
    def start_download(self):
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الفيديو")
            self.url_entry.focus()
            return
        
        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("خطأ", "هذا ليس رابط YouTube صحيح")
            self.url_entry.focus()
            return
        
        if not YT_DLP_AVAILABLE:
            messagebox.showerror("خطأ", "yt-dlp غير متوفر. قم بتثبيته: pip install yt-dlp")
            return
        
        # تعطيل أزرار التحكم
        self.download_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        
        def download():
            try:
                download_type = self.download_type_var.get()
                quality = self.quality_var.get()
                
                if download_type == "video":
                    self.status_var.set("🎥 بدء تحميل الفيديو...")
                    opts = self.ydl_opts_video.copy()
                    if quality != "best":
                        if quality.endswith('p'):
                            height = quality[:-1]
                            opts['format'] = f'best[height<={height}]'
                        elif quality == "worst":
                            opts['format'] = 'worst'
                    
                elif download_type == "audio":
                    self.status_var.set("🎵 بدء تحميل الصوت...")
                    opts = self.ydl_opts_audio.copy()
                
                elif download_type == "playlist":
                    self.status_var.set("📋 بدء تحميل قائمة التشغيل...")
                    opts = self.ydl_opts_video.copy()
                    opts['noplaylist'] = False
                
                # إضافة hook للتقدم
                opts['progress_hooks'] = [self.progress_hook]
                
                with yt_dlp.YoutubeDL(opts) as ydl:
                    ydl.download([url])
                
                self.progress_var.set(100)
                self.status_var.set("✅ اكتمل التحميل بنجاح!")
                messagebox.showinfo("نجح التحميل", f"✅ تم التحميل بنجاح!\n📁 المكان: {self.download_path}")
                
            except Exception as e:
                self.status_var.set("❌ فشل التحميل")
                messagebox.showerror("خطأ في التحميل", f"فشل التحميل:\n{str(e)}")
            
            finally:
                self.download_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
        
        threading.Thread(target=download, daemon=True).start()
    
    def progress_hook(self, d):
        """تحديث شريط التقدم"""
        if d['status'] == 'downloading':
            if 'total_bytes' in d:
                percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                self.progress_var.set(percent)
                self.status_var.set(f"📥 التحميل... {percent:.1f}%")
            elif '_percent_str' in d:
                percent_str = d['_percent_str'].strip('%')
                try:
                    percent = float(percent_str)
                    self.progress_var.set(percent)
                    self.status_var.set(f"📥 التحميل... {percent:.1f}%")
                except:
                    pass
        elif d['status'] == 'finished':
            self.progress_var.set(100)
            self.status_var.set("✅ انتهى التحميل!")
    
    def stop_download(self):
        """إيقاف التحميل"""
        # هذه الوظيفة تحتاج تطوير أكثر لإيقاف yt-dlp
        self.status_var.set("⏹️ تم طلب إيقاف التحميل")
        self.stop_btn.config(state=tk.DISABLED)


def main():
    """تشغيل الواجهة الرسومية"""
    root = tk.Tk()
    
    # تحسين مظهر النوافذ
    try:
        root.tk.call('tk', 'scaling', 1.2)  # تحسين الدقة
    except:
        pass
    
    # تعيين أيقونة (اختيارية)
    try:
        root.iconbitmap("youtube.ico")
    except:
        pass
    
    # التحقق من المكتبات المطلوبة
    if not YT_DLP_AVAILABLE:
        messagebox.showwarning("تحذير", 
                              "yt-dlp غير مثبت!\n\nقم بتثبيته باستخدام:\npip install yt-dlp\n\nثم أعد تشغيل البرنامج")
    
    app = FixedYouTubeGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
