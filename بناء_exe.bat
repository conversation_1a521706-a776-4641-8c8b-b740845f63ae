@echo off
chcp 65001 >nul
title بناء ملف exe - YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🏗️ بناء ملف exe                              ║
echo ║              YouTube Video Downloader                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

echo    تثبيت PyInstaller...
pip install pyinstaller

echo    تثبيت yt-dlp...
pip install yt-dlp

echo    تثبيت Pillow...
pip install Pillow

echo    تثبيت colorama...
pip install colorama

echo    تثبيت tqdm...
pip install tqdm

echo.
echo ✅ تم تثبيت جميع المكتبات
echo.

echo 🎨 إنشاء الشعارات...
python create_logo.py

echo.
echo 🔨 بناء الملف التنفيذي...
echo ⏳ هذا قد يستغرق عدة دقائق...
echo.

REM بناء ملف exe واحد
pyinstaller --onefile --windowed --name "YouTube_Video_Downloader" --icon "youtube_downloader.ico" --add-data "youtube_downloader_icon.png;." --add-data "youtube_downloader_banner.png;." --add-data "youtube_icon_small.png;." --hidden-import "yt_dlp" --hidden-import "PIL" --hidden-import "PIL.Image" --hidden-import "PIL.ImageTk" --hidden-import "colorama" --hidden-import "tqdm" gui_working.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في البناء
    echo.
    echo 🔄 محاولة بناء بسيط...
    pyinstaller --onefile --windowed --name "YouTube_Video_Downloader_Simple" gui_working.py
    
    if errorlevel 1 (
        echo ❌ فشل في البناء البسيط أيضاً
        echo.
        echo 💡 جرب:
        echo    1. تأكد من تثبيت جميع المكتبات
        echo    2. أعد تشغيل الكمبيوتر
        echo    3. شغّل كمدير
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم بناء الملف التنفيذي بنجاح!
echo.

REM التحقق من وجود الملف
if exist "dist\YouTube_Video_Downloader.exe" (
    echo 📁 الملف: dist\YouTube_Video_Downloader.exe
    
    REM حساب حجم الملف
    for %%A in ("dist\YouTube_Video_Downloader.exe") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo 📊 الحجم: !size_mb! MB تقريباً
    )
    
    echo.
    echo 🎉 البرنامج جاهز للاستخدام!
    echo.
    echo 💡 يمكنك الآن:
    echo    - تشغيل الملف مباشرة
    echo    - نسخه إلى أي مكان
    echo    - إنشاء اختصار على سطح المكتب
    echo    - مشاركته مع الآخرين
    echo.
    
    echo هل تريد تشغيل البرنامج الآن؟ (y/n)
    set /p run_now="اختر: "
    
    if /i "!run_now!"=="y" (
        echo.
        echo 🚀 تشغيل البرنامج...
        start "" "dist\YouTube_Video_Downloader.exe"
    )
    
    echo.
    echo هل تريد فتح مجلد الملف؟ (y/n)
    set /p open_folder="اختر: "
    
    if /i "!open_folder!"=="y" (
        start "" "dist"
    )
    
) else if exist "dist\YouTube_Video_Downloader_Simple.exe" (
    echo 📁 الملف: dist\YouTube_Video_Downloader_Simple.exe
    echo ⚠️ تم إنشاء نسخة بسيطة (بدون شعارات)
    
    echo.
    echo هل تريد تشغيل البرنامج؟ (y/n)
    set /p run_simple="اختر: "
    
    if /i "!run_simple!"=="y" (
        start "" "dist\YouTube_Video_Downloader_Simple.exe"
    )
    
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist\
)

echo.
echo 🧹 تنظيف الملفات المؤقتة...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del "*.spec"

echo ✅ تم التنظيف

echo.
echo 🙏 شكراً لاستخدام أداة بناء exe!
pause
