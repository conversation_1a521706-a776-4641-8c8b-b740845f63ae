# YouTube Video Downloader

A comprehensive Python tool for downloading YouTube videos, audio, and playlists with both command-line and graphical interfaces.

## 🌟 Features

### Core Features
- ✅ **Single Video Download** - Download individual YouTube videos in various qualities
- ✅ **Audio-Only Download** - Extract and download audio in MP3 or WebM format
- ✅ **Playlist Support** - Download entire playlists or specific number of videos
- ✅ **Quality Selection** - Choose from available video qualities (720p, 1080p, etc.)
- ✅ **Batch Processing** - Download multiple videos from a text file
- ✅ **Progress Tracking** - Real-time download progress with progress bars

### Advanced Features
- 🎯 **Subtitle Download** - Download subtitles in multiple languages
- 🔄 **Format Conversion** - Convert videos to different formats (AVI, MOV, etc.)
- 🎵 **Audio Extraction** - Extract audio from downloaded videos
- ⚙️ **Configuration Management** - Save and manage download preferences
- 📊 **Download Reports** - Generate detailed reports of batch downloads
- 🧹 **Cleanup Tools** - Remove temporary files automatically

### User Interfaces
- 💻 **Command Line Interface** - Full-featured CLI with colored output
- 🖥️ **Graphical Interface** - User-friendly GUI with progress bars
- 🔧 **Interactive Mode** - Step-by-step guided downloads

## 📋 Requirements

- Python 3.7 or higher
- Internet connection
- FFmpeg (optional, for advanced video processing)

## 🚀 Installation

1. **Clone or download this repository:**
   ```bash
   git clone <repository-url>
   cd youtube-downloader
   ```

2. **Install required packages:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Optional: Install FFmpeg for advanced features:**
   - Windows: Download from [FFmpeg website](https://ffmpeg.org/download.html)
   - macOS: `brew install ffmpeg`
   - Linux: `sudo apt install ffmpeg` (Ubuntu/Debian) or `sudo yum install ffmpeg` (CentOS/RHEL)

## 📖 Usage

### Command Line Interface

#### Basic Usage
```bash
# Interactive mode (recommended for beginners)
python main.py

# Download a single video
python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID"

# Download audio only
python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID" -a

# Download with specific quality
python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID" -q 720p

# Download playlist
python main.py -u "https://www.youtube.com/playlist?list=PLAYLIST_ID" -p

# Download from file
python main.py -f urls.txt

# Get video information only
python main.py -u "https://www.youtube.com/watch?v=VIDEO_ID" --info
```

#### Command Line Options
```
-u, --url          YouTube video or playlist URL
-a, --audio        Download audio only
-p, --playlist     Download as playlist
-q, --quality      Video quality (highest, lowest, 720p, 1080p, etc.)
-f, --file         File containing YouTube URLs (one per line)
-o, --output       Output directory (default: downloads)
--info             Show video information only
```

### Graphical User Interface

Launch the GUI application:
```bash
python gui.py
```

The GUI provides:
- URL input with video information preview
- Download type selection (Video/Audio/Playlist)
- Quality and format options
- Output directory selection
- Progress tracking with visual progress bar
- Batch download from file
- Download log with detailed information

### Advanced Features

Run the advanced features interface:
```bash
python advanced_features.py
```

Advanced options include:
- Download videos with automatic subtitle download
- Batch processing with custom options
- Configuration management
- Temporary file cleanup

## 📁 Project Structure

```
youtube-downloader/
│
├── main.py                 # Command-line interface
├── gui.py                  # Graphical user interface
├── downloader.py           # Core download functionality
├── advanced_features.py    # Advanced features and utilities
├── requirements.txt        # Python dependencies
├── README.md              # This documentation
│
└── downloads/             # Default download directory
    ├── video_files/
    ├── audio_files/
    └── subtitles/
```

## 🔧 Configuration

The advanced downloader supports configuration management:

```python
# Default configuration
{
    "default_quality": "highest",
    "default_audio_format": "mp3",
    "auto_subtitle_download": false,
    "preferred_subtitle_languages": ["en"],
    "auto_convert_format": null,
    "max_concurrent_downloads": 3
}
```

Configuration is automatically saved to `downloads/downloader_config.json`.

## 📝 Examples

### Example 1: Basic Video Download
```bash
python main.py -u "https://www.youtube.com/watch?v=dQw4w9WgXcQ" -q 720p -o "./my_videos"
```

### Example 2: Audio Playlist Download
```bash
python main.py -u "https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9JiYIk3HBzJyQcx" -a -p
```

### Example 3: Batch Download from File
Create a file `urls.txt`:
```
https://www.youtube.com/watch?v=VIDEO_ID_1
https://www.youtube.com/watch?v=VIDEO_ID_2
https://www.youtube.com/watch?v=VIDEO_ID_3
```

Then run:
```bash
python main.py -f urls.txt -q highest
```

### Example 4: Using the Core Library
```python
from downloader import YouTubeDownloader

# Create downloader instance
downloader = YouTubeDownloader("./downloads")

# Download a video
video_path = downloader.download_video(
    "https://www.youtube.com/watch?v=VIDEO_ID",
    quality="720p"
)

# Download audio
audio_path = downloader.download_audio(
    "https://www.youtube.com/watch?v=VIDEO_ID",
    format="mp3"
)

# Get video information
info = downloader.get_video_info("https://www.youtube.com/watch?v=VIDEO_ID")
print(f"Title: {info['title']}")
print(f"Duration: {info['length']} seconds")
```

## 🎯 Supported Formats

### Video Formats
- MP4 (default)
- AVI (via conversion)
- MOV (via conversion)
- WMV (via conversion)

### Audio Formats
- MP3 (default)
- WebM (original)
- WAV (via conversion)

### Subtitle Formats
- SRT (SubRip)

## ⚠️ Important Notes

1. **Legal Compliance**: Only download videos you have permission to download. Respect copyright laws and YouTube's Terms of Service.

2. **Rate Limiting**: The tool includes built-in delays to avoid overwhelming YouTube's servers. Don't modify these unless necessary.

3. **Quality Availability**: Not all videos have all quality options. The tool will automatically fall back to the highest available quality if the requested quality is not available.

4. **Large Files**: High-quality videos can be very large. Ensure you have sufficient disk space.

5. **Network Requirements**: Stable internet connection is required for reliable downloads.

## 🐛 Troubleshooting

### Common Issues

**"No suitable video stream found"**
- The video might be private, deleted, or region-restricted
- Try a different quality setting

**"Error downloading video: [SSL: CERTIFICATE_VERIFY_FAILED]"**
- Update your Python certificates
- Try running: `pip install --upgrade certifi`

**"ModuleNotFoundError: No module named 'pytube'"**
- Install requirements: `pip install -r requirements.txt`

**GUI doesn't start**
- Ensure tkinter is installed: `pip install tk`
- On Linux: `sudo apt-get install python3-tk`

### Getting Help

1. Check that all dependencies are installed correctly
2. Verify the YouTube URL is valid and accessible
3. Try downloading a different video to isolate the issue
4. Check your internet connection
5. Update pytube: `pip install --upgrade pytube`

## 📄 License

This project is for educational purposes. Please respect YouTube's Terms of Service and copyright laws when using this tool.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📞 Support

If you encounter any issues or have questions, please:
1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information about your problem

---

**Disclaimer**: This tool is for educational and personal use only. Users are responsible for complying with YouTube's Terms of Service and applicable copyright laws.
