#!/usr/bin/env python3
"""
واجهة رسومية سهلة لتحميل فيديوهات YouTube
مع حقل إدخال كبير وواضح للروابط
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from pathlib import Path
from downloader import YouTubeDownloader

class EasyYouTubeGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("أداة تحميل YouTube - واجهة سهلة")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # المتغيرات
        self.url_var = tk.StringVar()
        self.download_type_var = tk.StringVar(value="video")
        self.quality_var = tk.StringVar(value="highest")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="جاهز للتحميل")
        
        # إنشاء أداة التحميل
        self.download_path = "تحميلات_YouTube"
        Path(self.download_path).mkdir(exist_ok=True)
        self.downloader = YouTubeDownloader(self.download_path)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = tk.Label(main_frame, text="🎥 أداة تحميل فيديوهات YouTube", 
                              font=("Arial", 18, "bold"), fg="blue")
        title_label.pack(pady=(0, 20))
        
        # قسم إدخال الرابط
        url_frame = tk.LabelFrame(main_frame, text="📝 رابط الفيديو", 
                                 font=("Arial", 12, "bold"), padx=10, pady=10)
        url_frame.pack(fill=tk.X, pady=(0, 15))
        
        # تعليمات
        instruction_label = tk.Label(url_frame, 
                                   text="الصق رابط الفيديو من YouTube هنا:",
                                   font=("Arial", 10))
        instruction_label.pack(anchor=tk.W, pady=(0, 5))
        
        # حقل إدخال الرابط (كبير وواضح)
        self.url_entry = tk.Entry(url_frame, textvariable=self.url_var, 
                                 font=("Arial", 12), width=60, relief=tk.SUNKEN, bd=2)
        self.url_entry.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار سريعة
        button_frame = tk.Frame(url_frame)
        button_frame.pack(fill=tk.X)
        
        paste_btn = tk.Button(button_frame, text="📋 لصق", 
                             command=self.paste_url, bg="lightblue")
        paste_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(button_frame, text="🗑️ مسح", 
                             command=self.clear_url, bg="lightcoral")
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        info_btn = tk.Button(button_frame, text="ℹ️ معلومات الفيديو", 
                            command=self.show_video_info, bg="lightgreen")
        info_btn.pack(side=tk.LEFT)
        
        # قسم خيارات التحميل
        options_frame = tk.LabelFrame(main_frame, text="⚙️ خيارات التحميل", 
                                     font=("Arial", 12, "bold"), padx=10, pady=10)
        options_frame.pack(fill=tk.X, pady=(0, 15))
        
        # نوع التحميل
        type_frame = tk.Frame(options_frame)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(type_frame, text="نوع التحميل:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        radio_frame = tk.Frame(type_frame)
        radio_frame.pack(anchor=tk.W, pady=(5, 0))
        
        tk.Radiobutton(radio_frame, text="🎥 فيديو", variable=self.download_type_var, 
                      value="video", font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 20))
        tk.Radiobutton(radio_frame, text="🎵 صوت فقط", variable=self.download_type_var, 
                      value="audio", font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 20))
        tk.Radiobutton(radio_frame, text="📋 قائمة تشغيل", variable=self.download_type_var, 
                      value="playlist", font=("Arial", 10)).pack(side=tk.LEFT)
        
        # جودة الفيديو
        quality_frame = tk.Frame(options_frame)
        quality_frame.pack(fill=tk.X)
        
        tk.Label(quality_frame, text="جودة الفيديو:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var,
                                    values=["highest", "1080p", "720p", "480p", "360p", "lowest"],
                                    state="readonly", font=("Arial", 10))
        quality_combo.pack(anchor=tk.W, pady=(5, 0))
        
        # مجلد الحفظ
        save_frame = tk.LabelFrame(main_frame, text="📁 مجلد الحفظ", 
                                  font=("Arial", 12, "bold"), padx=10, pady=10)
        save_frame.pack(fill=tk.X, pady=(0, 15))
        
        path_frame = tk.Frame(save_frame)
        path_frame.pack(fill=tk.X)
        
        self.path_label = tk.Label(path_frame, text=f"المجلد: {self.download_path}", 
                                  font=("Arial", 9), anchor=tk.W)
        self.path_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = tk.Button(path_frame, text="📂 تغيير المجلد", 
                              command=self.browse_folder, bg="lightyellow")
        browse_btn.pack(side=tk.RIGHT)
        
        # شريط التقدم
        progress_frame = tk.LabelFrame(main_frame, text="📊 حالة التحميل", 
                                      font=("Arial", 12, "bold"), padx=10, pady=10)
        progress_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.status_label = tk.Label(progress_frame, textvariable=self.status_var, 
                                    font=("Arial", 10))
        self.status_label.pack(anchor=tk.W)
        
        # زر التحميل الكبير
        self.download_btn = tk.Button(main_frame, text="⬇️ تحميل الآن", 
                                     command=self.start_download,
                                     font=("Arial", 14, "bold"), 
                                     bg="green", fg="white", 
                                     height=2, width=20)
        self.download_btn.pack(pady=20)
        
        # ربط Enter بزر التحميل
        self.url_entry.bind('<Return>', lambda e: self.start_download())
        
        # تركيز على حقل الإدخال
        self.url_entry.focus()
    
    def paste_url(self):
        """لصق الرابط من الحافظة"""
        try:
            clipboard_content = self.root.clipboard_get()
            self.url_var.set(clipboard_content)
            self.status_var.set("تم لصق الرابط")
        except:
            messagebox.showwarning("تحذير", "لا يوجد محتوى في الحافظة")
    
    def clear_url(self):
        """مسح حقل الرابط"""
        self.url_var.set("")
        self.status_var.set("تم مسح الرابط")
        self.url_entry.focus()
    
    def browse_folder(self):
        """اختيار مجلد الحفظ"""
        folder = filedialog.askdirectory(initialdir=self.download_path)
        if folder:
            self.download_path = folder
            self.downloader = YouTubeDownloader(self.download_path)
            self.path_label.config(text=f"المجلد: {self.download_path}")
            self.status_var.set("تم تغيير مجلد الحفظ")
    
    def show_video_info(self):
        """عرض معلومات الفيديو"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الفيديو أولاً")
            return
        
        def get_info():
            try:
                self.status_var.set("جاري الحصول على معلومات الفيديو...")
                info = self.downloader.get_video_info(url)
                
                info_text = f"""
📹 العنوان: {info['title']}
👤 القناة: {info['author']}
⏱️ المدة: {info['length']} ثانية
👁️ المشاهدات: {info['views']:,}
🆔 معرف الفيديو: {info['video_id']}

📝 الوصف:
{info['description'][:200]}...
                """
                
                messagebox.showinfo("معلومات الفيديو", info_text)
                self.status_var.set("تم الحصول على معلومات الفيديو")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في الحصول على معلومات الفيديو:\n{str(e)}")
                self.status_var.set("خطأ في الحصول على المعلومات")
        
        threading.Thread(target=get_info, daemon=True).start()
    
    def progress_callback(self, stream, chunk, bytes_remaining):
        """تحديث شريط التقدم"""
        total_size = stream.filesize
        bytes_downloaded = total_size - bytes_remaining
        percentage = (bytes_downloaded / total_size) * 100
        
        self.progress_var.set(percentage)
        self.status_var.set(f"جاري التحميل... {percentage:.1f}%")
        self.root.update_idletasks()
    
    def start_download(self):
        """بدء التحميل"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الفيديو")
            self.url_entry.focus()
            return
        
        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("خطأ", "هذا ليس رابط YouTube صحيح")
            self.url_entry.focus()
            return
        
        # تعطيل زر التحميل
        self.download_btn.config(state="disabled", text="جاري التحميل...")
        self.progress_var.set(0)
        
        def download():
            try:
                download_type = self.download_type_var.get()
                quality = self.quality_var.get()
                
                if download_type == "video":
                    self.status_var.set("بدء تحميل الفيديو...")
                    filepath = self.downloader.download_video(url, quality, self.progress_callback)
                    success_msg = f"✅ تم تحميل الفيديو بنجاح!\n📁 المكان: {filepath}"
                    
                elif download_type == "audio":
                    self.status_var.set("بدء تحميل الصوت...")
                    filepath = self.downloader.download_audio(url, "mp3", self.progress_callback)
                    success_msg = f"✅ تم تحميل الصوت بنجاح!\n📁 المكان: {filepath}"
                    
                elif download_type == "playlist":
                    self.status_var.set("بدء تحميل قائمة التشغيل...")
                    filepaths = self.downloader.download_playlist(url, "video", quality)
                    success_msg = f"✅ تم تحميل {len(filepaths)} فيديو من القائمة!"
                
                self.progress_var.set(100)
                self.status_var.set("اكتمل التحميل بنجاح!")
                messagebox.showinfo("نجح التحميل", success_msg)
                
            except Exception as e:
                self.status_var.set("فشل التحميل")
                messagebox.showerror("خطأ في التحميل", f"فشل التحميل:\n{str(e)}")
            
            finally:
                self.download_btn.config(state="normal", text="⬇️ تحميل الآن")
        
        threading.Thread(target=download, daemon=True).start()

def main():
    """تشغيل الواجهة الرسومية"""
    root = tk.Tk()
    
    # تعيين أيقونة (اختيارية)
    try:
        root.iconbitmap("youtube.ico")  # إذا كان لديك أيقونة
    except:
        pass
    
    app = EasyYouTubeGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
