"""
YouTube Video Downloader Core Module
Provides functionality for downloading videos, audio, and playlists from YouTube
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Callable
from pytube import YouTube, Playlist
from pytube.exceptions import RegexMatchError, VideoUnavailable
from tqdm import tqdm

# Optional import for video processing
try:
    import moviepy.editor as mp
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("Warning: moviepy not available. Audio conversion and video format conversion will be limited.")


class YouTubeDownloader:
    """Main class for downloading YouTube videos and audio"""
    
    def __init__(self, download_path: str = "downloads"):
        """
        Initialize the downloader
        
        Args:
            download_path: Directory to save downloaded files
        """
        self.download_path = Path(download_path)
        self.download_path.mkdir(exist_ok=True)
        
    def sanitize_filename(self, filename: str) -> str:
        """Remove invalid characters from filename"""
        # Remove invalid characters for Windows/Linux
        invalid_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(invalid_chars, '_', filename)
        # Limit length to avoid filesystem issues
        return sanitized[:200] if len(sanitized) > 200 else sanitized
    
    def get_video_info(self, url: str) -> Dict:
        """
        Get video information without downloading
        
        Args:
            url: YouTube video URL
            
        Returns:
            Dictionary with video information
        """
        try:
            yt = YouTube(url)
            return {
                'title': yt.title,
                'author': yt.author,
                'length': yt.length,
                'views': yt.views,
                'description': yt.description[:200] + "..." if len(yt.description) > 200 else yt.description,
                'thumbnail_url': yt.thumbnail_url,
                'video_id': yt.video_id
            }
        except Exception as e:
            raise Exception(f"Error getting video info: {str(e)}")
    
    def get_available_streams(self, url: str) -> Dict:
        """
        Get available video and audio streams
        
        Args:
            url: YouTube video URL
            
        Returns:
            Dictionary with available streams
        """
        try:
            yt = YouTube(url)
            
            # Get video streams
            video_streams = []
            for stream in yt.streams.filter(progressive=True, file_extension='mp4'):
                video_streams.append({
                    'itag': stream.itag,
                    'resolution': stream.resolution,
                    'fps': stream.fps,
                    'filesize': stream.filesize,
                    'type': 'video'
                })
            
            # Get audio streams
            audio_streams = []
            for stream in yt.streams.filter(only_audio=True):
                audio_streams.append({
                    'itag': stream.itag,
                    'abr': stream.abr,
                    'filesize': stream.filesize,
                    'type': 'audio'
                })
            
            return {
                'video_streams': video_streams,
                'audio_streams': audio_streams
            }
        except Exception as e:
            raise Exception(f"Error getting streams: {str(e)}")
    
    def download_video(self, url: str, quality: str = 'highest', 
                      progress_callback: Optional[Callable] = None) -> str:
        """
        Download video from YouTube
        
        Args:
            url: YouTube video URL
            quality: Video quality ('highest', 'lowest', or specific resolution like '720p')
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Path to downloaded file
        """
        try:
            yt = YouTube(url, on_progress_callback=progress_callback)
            
            # Select stream based on quality preference
            if quality == 'highest':
                stream = yt.streams.get_highest_resolution()
            elif quality == 'lowest':
                stream = yt.streams.get_lowest_resolution()
            else:
                # Try to get specific resolution
                stream = yt.streams.filter(res=quality, file_extension='mp4').first()
                if not stream:
                    stream = yt.streams.get_highest_resolution()
            
            if not stream:
                raise Exception("No suitable video stream found")
            
            # Sanitize filename
            filename = self.sanitize_filename(yt.title) + '.mp4'
            filepath = self.download_path / filename
            
            # Download the video
            stream.download(output_path=str(self.download_path), filename=filename)
            
            return str(filepath)
            
        except Exception as e:
            raise Exception(f"Error downloading video: {str(e)}")
    
    def download_audio(self, url: str, format: str = 'mp3',
                      progress_callback: Optional[Callable] = None) -> str:
        """
        Download audio from YouTube video
        
        Args:
            url: YouTube video URL
            format: Audio format ('mp3' or 'wav')
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Path to downloaded audio file
        """
        try:
            yt = YouTube(url, on_progress_callback=progress_callback)
            
            # Get the best audio stream
            audio_stream = yt.streams.filter(only_audio=True).first()
            
            if not audio_stream:
                raise Exception("No audio stream found")
            
            # Sanitize filename
            temp_filename = self.sanitize_filename(yt.title) + '.webm'
            final_filename = self.sanitize_filename(yt.title) + f'.{format}'
            
            temp_filepath = self.download_path / temp_filename
            final_filepath = self.download_path / final_filename
            
            # Download audio
            audio_stream.download(output_path=str(self.download_path), filename=temp_filename)
            
            # Convert to desired format if needed
            if format == 'mp3' and MOVIEPY_AVAILABLE:
                audio_clip = mp.AudioFileClip(str(temp_filepath))
                audio_clip.write_audiofile(str(final_filepath), verbose=False, logger=None)
                audio_clip.close()

                # Remove temporary file
                temp_filepath.unlink()
            elif format == 'mp3' and not MOVIEPY_AVAILABLE:
                # If moviepy is not available, just rename to mp3 (it will still be webm format)
                print("Warning: moviepy not available. File will be saved as .mp3 but in WebM format.")
                temp_filepath.rename(final_filepath)
            else:
                # Just rename if keeping original format
                temp_filepath.rename(final_filepath)
            
            return str(final_filepath)
            
        except Exception as e:
            raise Exception(f"Error downloading audio: {str(e)}")
    
    def download_playlist(self, playlist_url: str, download_type: str = 'video',
                         quality: str = 'highest', max_videos: Optional[int] = None,
                         progress_callback: Optional[Callable] = None) -> List[str]:
        """
        Download entire playlist from YouTube
        
        Args:
            playlist_url: YouTube playlist URL
            download_type: 'video' or 'audio'
            quality: Video quality for video downloads
            max_videos: Maximum number of videos to download (None for all)
            progress_callback: Optional callback function for progress updates
            
        Returns:
            List of paths to downloaded files
        """
        try:
            playlist = Playlist(playlist_url)
            downloaded_files = []
            
            # Create playlist folder
            playlist_name = self.sanitize_filename(playlist.title or "Unknown_Playlist")
            playlist_path = self.download_path / playlist_name
            playlist_path.mkdir(exist_ok=True)
            
            # Temporarily change download path
            original_path = self.download_path
            self.download_path = playlist_path
            
            videos_to_download = list(playlist.video_urls)
            if max_videos:
                videos_to_download = videos_to_download[:max_videos]
            
            for i, video_url in enumerate(videos_to_download):
                try:
                    if progress_callback:
                        progress_callback(f"Downloading {i+1}/{len(videos_to_download)}")
                    
                    if download_type == 'video':
                        filepath = self.download_video(video_url, quality)
                    else:
                        filepath = self.download_audio(video_url)
                    
                    downloaded_files.append(filepath)
                    
                except Exception as e:
                    print(f"Error downloading video {i+1}: {str(e)}")
                    continue
            
            # Restore original download path
            self.download_path = original_path
            
            return downloaded_files
            
        except Exception as e:
            raise Exception(f"Error downloading playlist: {str(e)}")
    
    def download_from_file(self, file_path: str, download_type: str = 'video',
                          quality: str = 'highest') -> List[str]:
        """
        Download videos from a text file containing URLs
        
        Args:
            file_path: Path to text file with YouTube URLs (one per line)
            download_type: 'video' or 'audio'
            quality: Video quality for video downloads
            
        Returns:
            List of paths to downloaded files
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            downloaded_files = []
            
            for i, url in enumerate(urls):
                try:
                    print(f"Downloading {i+1}/{len(urls)}: {url}")
                    
                    if download_type == 'video':
                        filepath = self.download_video(url, quality)
                    else:
                        filepath = self.download_audio(url)
                    
                    downloaded_files.append(filepath)
                    
                except Exception as e:
                    print(f"Error downloading {url}: {str(e)}")
                    continue
            
            return downloaded_files
            
        except Exception as e:
            raise Exception(f"Error processing file: {str(e)}")


    def download_subtitles(self, url: str, languages: List[str] = None) -> List[str]:
        """
        Download subtitles for a YouTube video

        Args:
            url: YouTube video URL
            languages: List of language codes (e.g., ['en', 'es', 'fr']). If None, downloads all available

        Returns:
            List of paths to downloaded subtitle files
        """
        try:
            yt = YouTube(url)

            if not languages:
                # Get all available caption languages
                languages = list(yt.captions.keys())

            if not languages:
                raise Exception("No subtitles available for this video")

            downloaded_files = []
            video_title = self.sanitize_filename(yt.title)

            for lang in languages:
                try:
                    if lang in yt.captions:
                        caption = yt.captions[lang]

                        # Download as SRT format
                        srt_content = caption.generate_srt_captions()

                        # Save subtitle file
                        subtitle_filename = f"{video_title}_{lang}.srt"
                        subtitle_path = self.download_path / subtitle_filename

                        with open(subtitle_path, 'w', encoding='utf-8') as f:
                            f.write(srt_content)

                        downloaded_files.append(str(subtitle_path))

                except Exception as e:
                    print(f"Error downloading subtitle for language {lang}: {str(e)}")
                    continue

            return downloaded_files

        except Exception as e:
            raise Exception(f"Error downloading subtitles: {str(e)}")

    def get_available_subtitles(self, url: str) -> List[Dict]:
        """
        Get list of available subtitle languages

        Args:
            url: YouTube video URL

        Returns:
            List of dictionaries with subtitle information
        """
        try:
            yt = YouTube(url)
            subtitles = []

            for lang_code, caption in yt.captions.items():
                subtitles.append({
                    'language_code': lang_code,
                    'language_name': caption.name,
                    'is_generated': caption.code.endswith('.auto')
                })

            return subtitles

        except Exception as e:
            raise Exception(f"Error getting subtitle info: {str(e)}")

    def convert_video_format(self, input_path: str, output_format: str = 'avi') -> str:
        """
        Convert video to different format

        Args:
            input_path: Path to input video file
            output_format: Target format (avi, mov, wmv, etc.)

        Returns:
            Path to converted file
        """
        if not MOVIEPY_AVAILABLE:
            raise Exception("Video format conversion requires moviepy. Install with: pip install moviepy")

        try:
            input_path = Path(input_path)
            output_path = input_path.with_suffix(f'.{output_format}')

            # Load video
            video = mp.VideoFileClip(str(input_path))

            # Write in new format
            video.write_videofile(str(output_path), verbose=False, logger=None)
            video.close()

            return str(output_path)

        except Exception as e:
            raise Exception(f"Error converting video: {str(e)}")

    def extract_audio_from_video(self, video_path: str, audio_format: str = 'mp3') -> str:
        """
        Extract audio from video file

        Args:
            video_path: Path to video file
            audio_format: Audio format (mp3, wav, etc.)

        Returns:
            Path to extracted audio file
        """
        if not MOVIEPY_AVAILABLE:
            raise Exception("Audio extraction requires moviepy. Install with: pip install moviepy")

        try:
            video_path = Path(video_path)
            audio_path = video_path.with_suffix(f'.{audio_format}')

            # Load video and extract audio
            video = mp.VideoFileClip(str(video_path))
            audio = video.audio

            # Write audio file
            audio.write_audiofile(str(audio_path), verbose=False, logger=None)

            # Close clips
            audio.close()
            video.close()

            return str(audio_path)

        except Exception as e:
            raise Exception(f"Error extracting audio: {str(e)}")


def progress_function(stream, chunk, bytes_remaining):
    """Default progress callback function"""
    total_size = stream.filesize
    bytes_downloaded = total_size - bytes_remaining
    percentage = (bytes_downloaded / total_size) * 100
    print(f"\rDownloading... {percentage:.1f}%", end='', flush=True)
