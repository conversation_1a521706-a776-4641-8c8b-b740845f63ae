#!/usr/bin/env python3
"""
إنشاء اختصار على سطح المكتب لأداة تحميل YouTube
"""

import os
import sys
from pathlib import Path

def create_windows_shortcut():
    """إنشاء اختصار على Windows"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        # مسار الملف التنفيذي
        exe_path = Path("dist/YouTube_Video_Downloader.exe").absolute()
        
        if not exe_path.exists():
            print("❌ الملف التنفيذي غير موجود")
            print(f"ابحث عن: {exe_path}")
            return False
        
        # مسار سطح المكتب
        desktop = winshell.desktop()
        shortcut_path = Path(desktop) / "YouTube Video Downloader.lnk"
        
        # إنشاء الاختصار
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.Description = "أداة تحميل فيديوهات YouTube"
        
        # إضافة أيقونة إذا كانت متوفرة
        icon_path = Path("youtube_downloader.ico")
        if icon_path.exists():
            shortcut.IconLocation = str(icon_path.absolute())
        
        shortcut.save()
        
        print(f"✅ تم إنشاء الاختصار: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️ مكتبات Windows غير متوفرة")
        print("لتثبيتها: pip install pywin32 winshell")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختصار: {e}")
        return False

def create_simple_shortcut():
    """إنشاء اختصار بسيط باستخدام batch file"""
    try:
        # مسار الملف التنفيذي
        exe_path = Path("dist/YouTube_Video_Downloader.exe").absolute()
        
        if not exe_path.exists():
            print("❌ الملف التنفيذي غير موجود")
            return False
        
        # إنشاء ملف batch
        batch_content = f'''@echo off
title YouTube Video Downloader
cd /d "{exe_path.parent}"
start "" "{exe_path.name}"
'''
        
        # حفظ في سطح المكتب
        desktop_path = Path.home() / "Desktop"
        if not desktop_path.exists():
            desktop_path = Path.home() / "سطح المكتب"
        
        if desktop_path.exists():
            batch_path = desktop_path / "YouTube Video Downloader.bat"
            with open(batch_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"✅ تم إنشاء اختصار: {batch_path}")
            return True
        else:
            # حفظ في المجلد الحالي
            batch_path = Path("YouTube Video Downloader.bat")
            with open(batch_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"✅ تم إنشاء اختصار: {batch_path}")
            print("انسخه إلى سطح المكتب يدوياً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_linux_shortcut():
    """إنشاء اختصار على Linux"""
    try:
        # مسار الملف التنفيذي (أو Python script)
        script_path = Path("gui_working.py").absolute()
        
        if not script_path.exists():
            print("❌ ملف البرنامج غير موجود")
            return False
        
        # محتوى ملف .desktop
        desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=YouTube Video Downloader
Comment=أداة تحميل فيديوهات YouTube
Exec=python3 "{script_path}"
Icon={Path("youtube_downloader_icon.png").absolute() if Path("youtube_downloader_icon.png").exists() else "video"}
Terminal=false
Categories=AudioVideo;Video;
"""
        
        # مسار سطح المكتب
        desktop_path = Path.home() / "Desktop"
        if desktop_path.exists():
            shortcut_path = desktop_path / "youtube-downloader.desktop"
            with open(shortcut_path, 'w') as f:
                f.write(desktop_content)
            
            # جعل الملف قابل للتنفيذ
            os.chmod(shortcut_path, 0o755)
            
            print(f"✅ تم إنشاء اختصار: {shortcut_path}")
            return True
        else:
            print("❌ لم يتم العثور على سطح المكتب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_macos_shortcut():
    """إنشاء اختصار على macOS"""
    try:
        script_path = Path("gui_working.py").absolute()
        
        if not script_path.exists():
            print("❌ ملف البرنامج غير موجود")
            return False
        
        # إنشاء AppleScript
        applescript_content = f'''#!/usr/bin/osascript
tell application "Terminal"
    do script "cd '{script_path.parent}' && python3 '{script_path.name}'"
end tell
'''
        
        # حفظ على سطح المكتب
        desktop_path = Path.home() / "Desktop"
        shortcut_path = desktop_path / "YouTube Video Downloader.command"
        
        with open(shortcut_path, 'w') as f:
            f.write(applescript_content)
        
        # جعل الملف قابل للتنفيذ
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ تم إنشاء اختصار: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def install_to_start_menu():
    """إضافة البرنامج إلى قائمة ابدأ (Windows)"""
    if sys.platform != "win32":
        return False
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        exe_path = Path("dist/YouTube_Video_Downloader.exe").absolute()
        
        if not exe_path.exists():
            return False
        
        # مجلد قائمة ابدأ
        start_menu = winshell.start_menu()
        programs_folder = Path(start_menu) / "Programs"
        app_folder = programs_folder / "YouTube Video Downloader"
        
        # إنشاء مجلد التطبيق
        app_folder.mkdir(exist_ok=True)
        
        # إنشاء اختصار في قائمة ابدأ
        shortcut_path = app_folder / "YouTube Video Downloader.lnk"
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.Description = "أداة تحميل فيديوهات YouTube"
        
        icon_path = Path("youtube_downloader.ico")
        if icon_path.exists():
            shortcut.IconLocation = str(icon_path.absolute())
        
        shortcut.save()
        
        # إنشاء اختصار إلغاء التثبيت
        uninstall_path = app_folder / "Uninstall.lnk"
        uninstall_shortcut = shell.CreateShortCut(str(uninstall_path))
        uninstall_shortcut.Targetpath = "cmd.exe"
        uninstall_shortcut.Arguments = f'/c rmdir /s /q "{app_folder}" && echo تم إلغاء التثبيت && pause'
        uninstall_shortcut.Description = "إلغاء تثبيت YouTube Video Downloader"
        uninstall_shortcut.save()
        
        print(f"✅ تم إضافة البرنامج إلى قائمة ابدأ: {app_folder}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قائمة ابدأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔗 إنشاء اختصارات لأداة تحميل YouTube")
    print("=" * 50)
    
    # تحديد نظام التشغيل
    if sys.platform == "win32":
        print("🖥️ نظام Windows")
        
        print("\n1. إنشاء اختصار على سطح المكتب")
        if create_windows_shortcut():
            pass
        else:
            print("جاري المحاولة بطريقة بديلة...")
            create_simple_shortcut()
        
        print("\n2. إضافة إلى قائمة ابدأ")
        install_to_start_menu()
        
    elif sys.platform == "linux":
        print("🐧 نظام Linux")
        create_linux_shortcut()
        
    elif sys.platform == "darwin":
        print("🍎 نظام macOS")
        create_macos_shortcut()
        
    else:
        print(f"❓ نظام غير مدعوم: {sys.platform}")
        create_simple_shortcut()
    
    print("\n✅ تم إنشاء الاختصارات!")
    print("\n💡 يمكنك الآن:")
    print("   - النقر المزدوج على الاختصار لتشغيل البرنامج")
    print("   - سحب الاختصار إلى أي مكان")
    print("   - إرسال الاختصار للآخرين")

if __name__ == "__main__":
    main()
