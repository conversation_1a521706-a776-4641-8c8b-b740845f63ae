@echo off
chcp 65001 >nul
title أداة تحميل YouTube - واجهات محسّنة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎥 أداة تحميل YouTube                        ║
echo ║                   واجهات محسّنة                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo اختر الواجهة المناسبة لك:
echo.
echo 1. 🖥️  واجهة بسيطة جداً (سهلة الاستخدام)
echo 2. 🎨 واجهة محسّنة (مميزات متقدمة)
echo 3. 💻 واجهة نصية (سطر الأوامر)
echo 4. 🔧 حل مشكلة HTTP 400
echo 5. 📦 تثبيت المكتبات المطلوبة
echo 6. 🚪 خروج
echo.

set /p choice="اختر رقم (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل الواجهة البسيطة...
    python gui_simple.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل الواجهة المحسّنة...
    python gui_fixed.py
) else if "%choice%"=="3" (
    echo.
    echo 🚀 تشغيل الواجهة النصية...
    python تحميل_مضمون.py
) else if "%choice%"=="4" (
    echo.
    echo 🔧 حل مشكلة HTTP 400...
    call حل_المشكلة.bat
) else if "%choice%"=="5" (
    echo.
    echo 📦 تثبيت المكتبات...
    echo.
    echo جاري تثبيت yt-dlp...
    pip install yt-dlp
    echo.
    echo جاري تثبيت colorama...
    pip install colorama
    echo.
    echo ✅ تم تثبيت المكتبات بنجاح!
    echo.
    echo يمكنك الآن استخدام أي واجهة
    pause
    goto :start
) else if "%choice%"=="6" (
    echo.
    echo 👋 وداعاً!
    exit
) else (
    echo.
    echo ❌ اختيار غير صحيح
    pause
    goto :start
)

:start
echo.
echo هل تريد تشغيل واجهة أخرى؟ (y/n)
set /p again="اختر: "

if /i "%again%"=="y" (
    cls
    goto :eof
) else (
    echo.
    echo 🙏 شكراً لاستخدام أداة تحميل YouTube!
)

pause
