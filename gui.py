#!/usr/bin/env python3
"""
YouTube Video Downloader - Graphical User Interface
A user-friendly GUI for downloading YouTube videos, audio, and playlists
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from downloader import YouTubeDownloader


class YouTubeDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Video Downloader")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Initialize downloader
        self.downloader = YouTubeDownloader()
        self.download_path = "downloads"
        
        # Variables
        self.url_var = tk.StringVar()
        self.download_type_var = tk.StringVar(value="video")
        self.quality_var = tk.StringVar(value="highest")
        self.audio_format_var = tk.StringVar(value="mp3")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="YouTube Video Downloader", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # URL input section
        url_frame = ttk.LabelFrame(main_frame, text="Video/Playlist URL", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        url_frame.columnconfigure(0, weight=1)
        
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 10))
        self.url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        info_button = ttk.Button(url_frame, text="Get Info", command=self.get_video_info)
        info_button.grid(row=0, column=1)
        
        # Download options section
        options_frame = ttk.LabelFrame(main_frame, text="Download Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Download type
        ttk.Label(options_frame, text="Type:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        type_frame = ttk.Frame(options_frame)
        type_frame.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Radiobutton(type_frame, text="Video", variable=self.download_type_var, 
                       value="video", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Audio Only", variable=self.download_type_var, 
                       value="audio", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Playlist", variable=self.download_type_var, 
                       value="playlist", command=self.on_type_change).pack(side=tk.LEFT)
        
        # Quality selection (for video)
        ttk.Label(options_frame, text="Quality:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.quality_combo = ttk.Combobox(options_frame, textvariable=self.quality_var, 
                                         values=["highest", "lowest", "720p", "1080p", "480p", "360p"],
                                         state="readonly", width=15)
        self.quality_combo.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # Audio format selection (for audio)
        ttk.Label(options_frame, text="Audio Format:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.audio_format_combo = ttk.Combobox(options_frame, textvariable=self.audio_format_var,
                                              values=["mp3", "webm"], state="readonly", width=15)
        self.audio_format_combo.grid(row=2, column=1, sticky=tk.W, pady=(10, 0))
        
        # Output directory section
        output_frame = ttk.LabelFrame(main_frame, text="Output Directory", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_label = ttk.Label(output_frame, text=f"Downloads will be saved to: {self.download_path}")
        self.output_label.grid(row=0, column=0, sticky=tk.W)
        
        browse_button = ttk.Button(output_frame, text="Browse", command=self.browse_output_directory)
        browse_button.grid(row=0, column=1, padx=(10, 0))
        
        # Batch download section
        batch_frame = ttk.LabelFrame(main_frame, text="Batch Download", padding="10")
        batch_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        batch_frame.columnconfigure(0, weight=1)
        
        batch_info = ttk.Label(batch_frame, text="Select a text file with YouTube URLs (one per line)")
        batch_info.grid(row=0, column=0, sticky=tk.W)
        
        batch_button = ttk.Button(batch_frame, text="Select File & Download", command=self.batch_download)
        batch_button.grid(row=0, column=1, padx=(10, 0))
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Download button
        self.download_button = ttk.Button(main_frame, text="Download", command=self.start_download,
                                         style="Accent.TButton")
        self.download_button.grid(row=6, column=0, columnspan=3, pady=20)
        
        # Log section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Initial UI state
        self.on_type_change()
        
    def on_type_change(self):
        """Handle download type change"""
        download_type = self.download_type_var.get()
        
        if download_type == "audio":
            self.quality_combo.config(state="disabled")
            self.audio_format_combo.config(state="readonly")
        else:
            self.quality_combo.config(state="readonly")
            self.audio_format_combo.config(state="disabled")
    
    def browse_output_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(initialdir=self.download_path)
        if directory:
            self.download_path = directory
            self.downloader = YouTubeDownloader(self.download_path)
            self.output_label.config(text=f"Downloads will be saved to: {self.download_path}")
            self.log(f"Output directory changed to: {self.download_path}")
    
    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """Update status label"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value):
        """Update progress bar"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def progress_callback(self, stream, chunk, bytes_remaining):
        """Progress callback for downloads"""
        total_size = stream.filesize
        bytes_downloaded = total_size - bytes_remaining
        percentage = (bytes_downloaded / total_size) * 100
        self.update_progress(percentage)
        self.update_status(f"Downloading... {percentage:.1f}%")
    
    def get_video_info(self):
        """Get and display video information"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return
        
        def get_info():
            try:
                self.update_status("Getting video information...")
                info = self.downloader.get_video_info(url)
                
                info_text = f"""
Title: {info['title']}
Author: {info['author']}
Duration: {info['length']} seconds
Views: {info['views']:,}
Video ID: {info['video_id']}

Description:
{info['description']}
                """
                
                # Show info in a new window
                info_window = tk.Toplevel(self.root)
                info_window.title("Video Information")
                info_window.geometry("600x400")
                
                text_widget = scrolledtext.ScrolledText(info_window, wrap=tk.WORD)
                text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                text_widget.insert(tk.END, info_text)
                text_widget.config(state=tk.DISABLED)
                
                self.update_status("Video information retrieved")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to get video info: {str(e)}")
                self.update_status("Error getting video info")
        
        threading.Thread(target=get_info, daemon=True).start()
    
    def start_download(self):
        """Start download in a separate thread"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return
        
        # Disable download button during download
        self.download_button.config(state="disabled")
        self.update_progress(0)
        
        def download():
            try:
                download_type = self.download_type_var.get()
                
                if download_type == "video":
                    self.log(f"Starting video download: {url}")
                    quality = self.quality_var.get()
                    filepath = self.downloader.download_video(url, quality, self.progress_callback)
                    self.log(f"Video downloaded successfully: {filepath}")
                    
                elif download_type == "audio":
                    self.log(f"Starting audio download: {url}")
                    audio_format = self.audio_format_var.get()
                    filepath = self.downloader.download_audio(url, audio_format, self.progress_callback)
                    self.log(f"Audio downloaded successfully: {filepath}")
                    
                elif download_type == "playlist":
                    self.log(f"Starting playlist download: {url}")
                    quality = self.quality_var.get()
                    filepaths = self.downloader.download_playlist(url, "video", quality, 
                                                                progress_callback=self.playlist_progress)
                    self.log(f"Playlist downloaded successfully: {len(filepaths)} files")
                
                self.update_status("Download completed successfully!")
                self.update_progress(100)
                messagebox.showinfo("Success", "Download completed successfully!")
                
            except Exception as e:
                self.log(f"Download failed: {str(e)}")
                self.update_status("Download failed")
                messagebox.showerror("Error", f"Download failed: {str(e)}")
            
            finally:
                self.download_button.config(state="normal")
        
        threading.Thread(target=download, daemon=True).start()
    
    def playlist_progress(self, message):
        """Progress callback for playlist downloads"""
        self.update_status(message)
        self.log(message)
    
    def batch_download(self):
        """Download from a file containing URLs"""
        file_path = filedialog.askopenfilename(
            title="Select file with YouTube URLs",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        # Disable download button during download
        self.download_button.config(state="disabled")
        self.update_progress(0)
        
        def download():
            try:
                download_type = "audio" if self.download_type_var.get() == "audio" else "video"
                quality = self.quality_var.get()
                
                self.log(f"Starting batch download from: {file_path}")
                filepaths = self.downloader.download_from_file(file_path, download_type, quality)
                self.log(f"Batch download completed: {len(filepaths)} files")
                
                self.update_status("Batch download completed!")
                self.update_progress(100)
                messagebox.showinfo("Success", f"Batch download completed! Downloaded {len(filepaths)} files.")
                
            except Exception as e:
                self.log(f"Batch download failed: {str(e)}")
                self.update_status("Batch download failed")
                messagebox.showerror("Error", f"Batch download failed: {str(e)}")
            
            finally:
                self.download_button.config(state="normal")
        
        threading.Thread(target=download, daemon=True).start()


def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    
    # Set theme
    style = ttk.Style()
    if "clam" in style.theme_names():
        style.theme_use("clam")
    
    app = YouTubeDownloaderGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
