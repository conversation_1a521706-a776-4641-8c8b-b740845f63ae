#!/usr/bin/env python3
"""
YouTube Video Downloader - Graphical User Interface
A user-friendly GUI for downloading YouTube videos, audio, and playlists
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path

# محاولة استيراد المكتبات المطلوبة
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False

try:
    from downloader import YouTubeDownloader
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False


class YouTubeDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Video Downloader")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # Initialize downloader
        self.download_path = "downloads"
        Path(self.download_path).mkdir(exist_ok=True)

        # تحديد أي مكتبة متوفرة
        if YT_DLP_AVAILABLE:
            self.downloader_type = "yt-dlp"
            self.setup_yt_dlp()
        elif <PERSON>Y<PERSON>BE_AVAILABLE:
            self.downloader_type = "pytube"
            self.downloader = YouTubeDownloader(self.download_path)
        else:
            self.downloader_type = None

        # Variables
        self.url_var = tk.StringVar()
        self.download_type_var = tk.StringVar(value="video")
        self.quality_var = tk.StringVar(value="highest")
        self.audio_format_var = tk.StringVar(value="mp3")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")

        self.setup_ui()

        # التحقق من المكتبات عند البدء
        self.check_dependencies()

    def setup_yt_dlp(self):
        """إعداد yt-dlp"""
        self.ydl_opts_video = {
            'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
            'format': 'best[height<=1080]',
            'noplaylist': True,
        }

        self.ydl_opts_audio = {
            'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'noplaylist': True,
        }

    def check_dependencies(self):
        """التحقق من المكتبات المطلوبة"""
        if not YT_DLP_AVAILABLE and not PYTUBE_AVAILABLE:
            self.log("⚠️ Warning: No download libraries available")
            self.log("Please install yt-dlp: pip install yt-dlp")
            self.status_var.set("No download libraries - Install yt-dlp")
        elif YT_DLP_AVAILABLE:
            self.log("✅ Using yt-dlp (recommended)")
            self.status_var.set("Ready - Using yt-dlp")
        elif PYTUBE_AVAILABLE:
            self.log("⚠️ Using pytube (may have issues)")
            self.status_var.set("Ready - Using pytube")
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="YouTube Video Downloader", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # URL input section
        url_frame = ttk.LabelFrame(main_frame, text="Video/Playlist URL", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        url_frame.columnconfigure(0, weight=1)
        
        # حقل إدخال الرابط محسّن
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 11))
        self.url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        # أزرار مساعدة
        button_frame = ttk.Frame(url_frame)
        button_frame.grid(row=0, column=1, padx=(5, 0))

        paste_button = ttk.Button(button_frame, text="Paste", command=self.paste_url)
        paste_button.pack(side=tk.LEFT, padx=(0, 5))

        info_button = ttk.Button(button_frame, text="Get Info", command=self.get_video_info)
        info_button.pack(side=tk.LEFT, padx=(0, 5))

        clear_button = ttk.Button(button_frame, text="Clear", command=self.clear_url)
        clear_button.pack(side=tk.LEFT)

        # إضافة placeholder text
        self.url_entry.insert(0, "Paste YouTube URL here...")
        self.url_entry.bind('<FocusIn>', self.on_entry_focus_in)
        self.url_entry.bind('<FocusOut>', self.on_entry_focus_out)
        self.url_entry.bind('<KeyRelease>', self.on_url_change)
        self.url_entry.bind('<Control-v>', lambda e: self.root.after(10, self.paste_url))

        # تركيز على حقل الإدخال
        self.root.after(100, lambda: self.url_entry.focus())
        
        # Download options section
        options_frame = ttk.LabelFrame(main_frame, text="Download Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Download type
        ttk.Label(options_frame, text="Type:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        type_frame = ttk.Frame(options_frame)
        type_frame.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Radiobutton(type_frame, text="Video", variable=self.download_type_var, 
                       value="video", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Audio Only", variable=self.download_type_var, 
                       value="audio", command=self.on_type_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Playlist", variable=self.download_type_var, 
                       value="playlist", command=self.on_type_change).pack(side=tk.LEFT)
        
        # Quality selection (for video)
        ttk.Label(options_frame, text="Quality:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.quality_combo = ttk.Combobox(options_frame, textvariable=self.quality_var, 
                                         values=["highest", "lowest", "720p", "1080p", "480p", "360p"],
                                         state="readonly", width=15)
        self.quality_combo.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # Audio format selection (for audio)
        ttk.Label(options_frame, text="Audio Format:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.audio_format_combo = ttk.Combobox(options_frame, textvariable=self.audio_format_var,
                                              values=["mp3", "webm"], state="readonly", width=15)
        self.audio_format_combo.grid(row=2, column=1, sticky=tk.W, pady=(10, 0))
        
        # Output directory section
        output_frame = ttk.LabelFrame(main_frame, text="Output Directory", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_label = ttk.Label(output_frame, text=f"Downloads will be saved to: {self.download_path}")
        self.output_label.grid(row=0, column=0, sticky=tk.W)
        
        browse_button = ttk.Button(output_frame, text="Browse", command=self.browse_output_directory)
        browse_button.grid(row=0, column=1, padx=(10, 0))
        
        # Batch download section
        batch_frame = ttk.LabelFrame(main_frame, text="Batch Download", padding="10")
        batch_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        batch_frame.columnconfigure(0, weight=1)
        
        batch_info = ttk.Label(batch_frame, text="Select a text file with YouTube URLs (one per line)")
        batch_info.grid(row=0, column=0, sticky=tk.W)
        
        batch_button = ttk.Button(batch_frame, text="Select File & Download", command=self.batch_download)
        batch_button.grid(row=0, column=1, padx=(10, 0))
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Download button
        self.download_button = ttk.Button(main_frame, text="Download", command=self.start_download,
                                         style="Accent.TButton")
        self.download_button.grid(row=6, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))

        # Install yt-dlp button (if not available)
        if not YT_DLP_AVAILABLE:
            install_button = ttk.Button(main_frame, text="Install yt-dlp (Recommended)",
                                       command=self.install_yt_dlp)
            install_button.grid(row=6, column=2, pady=20, padx=(10, 0), sticky=(tk.W, tk.E))
        
        # Log section
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Initial UI state
        self.on_type_change()

    def paste_url(self):
        """Paste URL from clipboard"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.clear_placeholder()
                self.url_var.set(clipboard_content.strip())
                self.update_status("URL pasted from clipboard")
            else:
                messagebox.showwarning("Warning", "Clipboard is empty")
        except tk.TclError:
            messagebox.showwarning("Warning", "Cannot access clipboard")

    def clear_url(self):
        """Clear URL field"""
        self.url_var.set("")
        self.add_placeholder()
        self.update_status("URL cleared")
        self.url_entry.focus()

    def on_entry_focus_in(self, event):
        """Handle focus in event"""
        self.clear_placeholder()

    def on_entry_focus_out(self, event):
        """Handle focus out event"""
        if not self.url_var.get().strip():
            self.add_placeholder()

    def on_url_change(self, event):
        """Handle URL change"""
        url = self.url_var.get().strip()
        if url and url != "Paste YouTube URL here...":
            if "youtube.com" in url or "youtu.be" in url:
                self.update_status("✅ Valid YouTube URL")
            else:
                self.update_status("⚠️ This doesn't look like a YouTube URL")
        else:
            self.update_status("Ready")

    def clear_placeholder(self):
        """Clear placeholder text"""
        if self.url_var.get() == "Paste YouTube URL here...":
            self.url_var.set("")

    def add_placeholder(self):
        """Add placeholder text"""
        if not self.url_var.get().strip():
            self.url_var.set("Paste YouTube URL here...")
        
    def on_type_change(self):
        """Handle download type change"""
        download_type = self.download_type_var.get()
        
        if download_type == "audio":
            self.quality_combo.config(state="disabled")
            self.audio_format_combo.config(state="readonly")
        else:
            self.quality_combo.config(state="readonly")
            self.audio_format_combo.config(state="disabled")
    
    def browse_output_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(initialdir=self.download_path)
        if directory:
            self.download_path = directory
            self.downloader = YouTubeDownloader(self.download_path)
            self.output_label.config(text=f"Downloads will be saved to: {self.download_path}")
            self.log(f"Output directory changed to: {self.download_path}")
    
    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """Update status label"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value):
        """Update progress bar"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def progress_callback(self, stream, chunk, bytes_remaining):
        """Progress callback for downloads"""
        total_size = stream.filesize
        bytes_downloaded = total_size - bytes_remaining
        percentage = (bytes_downloaded / total_size) * 100
        self.update_progress(percentage)
        self.update_status(f"Downloading... {percentage:.1f}%")
    
    def get_video_info(self):
        """Get and display video information"""
        url = self.url_var.get().strip()
        if not url or url == "Paste YouTube URL here...":
            messagebox.showerror("Error", "Please enter a YouTube URL")
            self.url_entry.focus()
            return

        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            self.url_entry.focus()
            return

        if not YT_DLP_AVAILABLE and not PYTUBE_AVAILABLE:
            messagebox.showerror("Error", "No download libraries available. Install yt-dlp: pip install yt-dlp")
            return

        def get_info():
            try:
                self.update_status("Getting video information...")

                if self.downloader_type == "yt-dlp":
                    # استخدام yt-dlp
                    with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                        info = ydl.extract_info(url, download=False)
                        info_data = {
                            'title': info.get('title', 'Unknown'),
                            'author': info.get('uploader', 'Unknown'),
                            'length': info.get('duration', 0),
                            'views': info.get('view_count', 0),
                            'video_id': info.get('id', ''),
                            'description': info.get('description', '')[:200] + "..."
                        }
                else:
                    # استخدام pytube
                    info_data = self.downloader.get_video_info(url)

                info_text = f"""
Title: {info_data['title']}
Author: {info_data['author']}
Duration: {info_data['length']} seconds
Views: {info_data['views']:,}
Video ID: {info_data['video_id']}

Description:
{info_data['description']}
                """

                # Show info in a new window
                info_window = tk.Toplevel(self.root)
                info_window.title("Video Information")
                info_window.geometry("600x400")

                text_widget = scrolledtext.ScrolledText(info_window, wrap=tk.WORD)
                text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                text_widget.insert(tk.END, info_text)
                text_widget.config(state=tk.DISABLED)

                self.update_status("Video information retrieved")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to get video info: {str(e)}")
                self.update_status("Error getting video info")

        threading.Thread(target=get_info, daemon=True).start()
    
    def start_download(self):
        """Start download in a separate thread"""
        url = self.url_var.get().strip()
        if not url or url == "Paste YouTube URL here...":
            messagebox.showerror("Error", "Please enter a YouTube URL")
            self.url_entry.focus()
            return

        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("Error", "Please enter a valid YouTube URL")
            self.url_entry.focus()
            return

        if not YT_DLP_AVAILABLE and not PYTUBE_AVAILABLE:
            messagebox.showerror("Error", "No download libraries available. Install yt-dlp: pip install yt-dlp")
            return

        # Disable download button during download
        self.download_button.config(state="disabled")
        self.update_progress(0)

        def download():
            try:
                download_type = self.download_type_var.get()
                quality = self.quality_var.get()

                if self.downloader_type == "yt-dlp":
                    # استخدام yt-dlp
                    if download_type == "video":
                        self.log(f"Starting video download with yt-dlp: {url}")
                        opts = self.ydl_opts_video.copy()

                        # تحديد الجودة
                        if quality == "highest":
                            opts['format'] = 'best'
                        elif quality == "lowest":
                            opts['format'] = 'worst'
                        elif quality.endswith('p'):
                            height = quality[:-1]
                            opts['format'] = f'best[height<={height}]'

                        # إضافة progress hook
                        opts['progress_hooks'] = [self.yt_dlp_progress_hook]

                        with yt_dlp.YoutubeDL(opts) as ydl:
                            ydl.download([url])

                        self.log("Video downloaded successfully with yt-dlp")

                    elif download_type == "audio":
                        self.log(f"Starting audio download with yt-dlp: {url}")
                        opts = self.ydl_opts_audio.copy()
                        opts['progress_hooks'] = [self.yt_dlp_progress_hook]

                        with yt_dlp.YoutubeDL(opts) as ydl:
                            ydl.download([url])

                        self.log("Audio downloaded successfully with yt-dlp")

                    elif download_type == "playlist":
                        self.log(f"Starting playlist download with yt-dlp: {url}")
                        opts = self.ydl_opts_video.copy()
                        opts['noplaylist'] = False
                        opts['progress_hooks'] = [self.yt_dlp_progress_hook]

                        with yt_dlp.YoutubeDL(opts) as ydl:
                            ydl.download([url])

                        self.log("Playlist downloaded successfully with yt-dlp")

                else:
                    # استخدام pytube (النسخة الأصلية)
                    if download_type == "video":
                        self.log(f"Starting video download with pytube: {url}")
                        filepath = self.downloader.download_video(url, quality, self.progress_callback)
                        self.log(f"Video downloaded successfully: {filepath}")

                    elif download_type == "audio":
                        self.log(f"Starting audio download with pytube: {url}")
                        audio_format = self.audio_format_var.get()
                        filepath = self.downloader.download_audio(url, audio_format, self.progress_callback)
                        self.log(f"Audio downloaded successfully: {filepath}")

                    elif download_type == "playlist":
                        self.log(f"Starting playlist download with pytube: {url}")
                        filepaths = self.downloader.download_playlist(url, "video", quality,
                                                                    progress_callback=self.playlist_progress)
                        self.log(f"Playlist downloaded successfully: {len(filepaths)} files")

                self.update_status("Download completed successfully!")
                self.update_progress(100)
                messagebox.showinfo("Success", "Download completed successfully!")

            except Exception as e:
                self.log(f"Download failed: {str(e)}")
                self.update_status("Download failed")
                messagebox.showerror("Error", f"Download failed: {str(e)}")

            finally:
                self.download_button.config(state="normal")

        threading.Thread(target=download, daemon=True).start()

    def yt_dlp_progress_hook(self, d):
        """Progress hook for yt-dlp"""
        if d['status'] == 'downloading':
            if 'total_bytes' in d:
                percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                self.update_progress(percent)
                self.update_status(f"Downloading... {percent:.1f}%")
            elif '_percent_str' in d:
                percent_str = d['_percent_str'].strip('%')
                try:
                    percent = float(percent_str)
                    self.update_progress(percent)
                    self.update_status(f"Downloading... {percent:.1f}%")
                except:
                    pass
        elif d['status'] == 'finished':
            self.update_progress(100)
            self.update_status("Download finished!")

    def playlist_progress(self, message):
        """Progress callback for playlist downloads"""
        self.update_status(message)
        self.log(message)
    
    def batch_download(self):
        """Download from a file containing URLs"""
        file_path = filedialog.askopenfilename(
            title="Select file with YouTube URLs",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        # Disable download button during download
        self.download_button.config(state="disabled")
        self.update_progress(0)
        
        def download():
            try:
                download_type = "audio" if self.download_type_var.get() == "audio" else "video"
                quality = self.quality_var.get()
                
                self.log(f"Starting batch download from: {file_path}")
                filepaths = self.downloader.download_from_file(file_path, download_type, quality)
                self.log(f"Batch download completed: {len(filepaths)} files")
                
                self.update_status("Batch download completed!")
                self.update_progress(100)
                messagebox.showinfo("Success", f"Batch download completed! Downloaded {len(filepaths)} files.")
                
            except Exception as e:
                self.log(f"Batch download failed: {str(e)}")
                self.update_status("Batch download failed")
                messagebox.showerror("Error", f"Batch download failed: {str(e)}")
            
            finally:
                self.download_button.config(state="normal")
        
        threading.Thread(target=download, daemon=True).start()

    def install_yt_dlp(self):
        """Install yt-dlp from within the GUI"""
        def install():
            try:
                self.update_status("Installing yt-dlp...")
                self.log("Installing yt-dlp...")

                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])

                self.log("✅ yt-dlp installed successfully!")
                self.update_status("yt-dlp installed - Please restart the application")
                messagebox.showinfo("Success", "yt-dlp installed successfully!\n\nPlease restart the application to use it.")

            except Exception as e:
                self.log(f"❌ Failed to install yt-dlp: {str(e)}")
                self.update_status("Failed to install yt-dlp")
                messagebox.showerror("Error", f"Failed to install yt-dlp:\n{str(e)}")

        result = messagebox.askyesno("Install yt-dlp",
                                   "yt-dlp is not installed. It's more reliable than pytube.\n\nDo you want to install it now?")
        if result:
            threading.Thread(target=install, daemon=True).start()


def main():
    """Main function to run the GUI"""
    root = tk.Tk()

    # Set theme
    style = ttk.Style()
    if "clam" in style.theme_names():
        style.theme_use("clam")

    # التحقق من المكتبات قبل بدء الواجهة
    if not YT_DLP_AVAILABLE and not PYTUBE_AVAILABLE:
        result = messagebox.askyesno("Missing Libraries",
                                   "No download libraries found!\n\n" +
                                   "yt-dlp (recommended): More stable and reliable\n" +
                                   "pytube: May have issues with some videos\n\n" +
                                   "Do you want to install yt-dlp now?")
        if result:
            try:
                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
                messagebox.showinfo("Success", "yt-dlp installed successfully!\n\nRestart the application to use it.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to install yt-dlp:\n{str(e)}")
            root.destroy()
            return

    app = YouTubeDownloaderGUI(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
