#!/usr/bin/env python3
"""
واجهة رسومية بسيطة جداً لتحميل YouTube
تركز على سهولة إدخال الرابط
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import os
from pathlib import Path

# محاولة استيراد yt-dlp
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False


class SimpleYouTubeGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎥 تحميل YouTube - واجهة بسيطة")
        self.root.geometry("600x500")
        self.root.configure(bg="#F0F8FF")
        
        # مجلد التحميل
        self.download_path = "تحميلات_YouTube"
        Path(self.download_path).mkdir(exist_ok=True)
        
        # متغيرات
        self.url_var = tk.StringVar()
        self.is_downloading = False
        
        self.create_ui()
        
        # تركيز على حقل الإدخال
        self.root.after(100, lambda: self.url_entry.focus())
    
    def create_ui(self):
        """إنشاء الواجهة"""
        # العنوان
        title_label = tk.Label(self.root, text="🎥 أداة تحميل فيديوهات YouTube", 
                              font=("Arial", 16, "bold"), bg="#F0F8FF", fg="#2E86AB")
        title_label.pack(pady=20)
        
        # تعليمات
        instruction_label = tk.Label(self.root, 
                                   text="الصق رابط الفيديو من YouTube في الحقل أدناه:",
                                   font=("Arial", 12), bg="#F0F8FF", fg="#333")
        instruction_label.pack(pady=(0, 10))
        
        # إطار حقل الإدخال
        entry_frame = tk.Frame(self.root, bg="#FFFFFF", relief=tk.SUNKEN, bd=3)
        entry_frame.pack(pady=10, padx=20, fill=tk.X)
        
        # حقل إدخال الرابط - كبير جداً
        self.url_entry = tk.Entry(entry_frame, textvariable=self.url_var, 
                                 font=("Arial", 14), width=50, 
                                 relief=tk.FLAT, bd=10, bg="#FFFFFF")
        self.url_entry.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار مساعدة
        buttons_frame1 = tk.Frame(self.root, bg="#F0F8FF")
        buttons_frame1.pack(pady=10)
        
        # زر لصق كبير
        paste_btn = tk.Button(buttons_frame1, text="📋 لصق الرابط", 
                             command=self.paste_url, 
                             font=("Arial", 12, "bold"), 
                             bg="#4CAF50", fg="white", 
                             width=15, height=2, relief=tk.RAISED, bd=3)
        paste_btn.pack(side=tk.LEFT, padx=10)
        
        # زر مسح
        clear_btn = tk.Button(buttons_frame1, text="🗑️ مسح", 
                             command=self.clear_url,
                             font=("Arial", 12, "bold"), 
                             bg="#FF6B6B", fg="white", 
                             width=10, height=2, relief=tk.RAISED, bd=3)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        # خيارات التحميل
        options_frame = tk.Frame(self.root, bg="#E8F5E8", relief=tk.RAISED, bd=2)
        options_frame.pack(pady=20, padx=20, fill=tk.X)
        
        tk.Label(options_frame, text="اختر نوع التحميل:", 
                font=("Arial", 12, "bold"), bg="#E8F5E8").pack(pady=10)
        
        # أزرار التحميل الكبيرة
        buttons_frame2 = tk.Frame(options_frame, bg="#E8F5E8")
        buttons_frame2.pack(pady=10)
        
        # زر تحميل فيديو
        video_btn = tk.Button(buttons_frame2, text="🎥\nتحميل فيديو", 
                             command=lambda: self.download("video"),
                             font=("Arial", 14, "bold"), 
                             bg="#2196F3", fg="white", 
                             width=12, height=3, relief=tk.RAISED, bd=4)
        video_btn.pack(side=tk.LEFT, padx=20)
        
        # زر تحميل صوت
        audio_btn = tk.Button(buttons_frame2, text="🎵\nتحميل صوت", 
                             command=lambda: self.download("audio"),
                             font=("Arial", 14, "bold"), 
                             bg="#FF9800", fg="white", 
                             width=12, height=3, relief=tk.RAISED, bd=4)
        audio_btn.pack(side=tk.LEFT, padx=20)
        
        # معلومات المجلد
        folder_frame = tk.Frame(self.root, bg="#FFF8DC", relief=tk.RAISED, bd=2)
        folder_frame.pack(pady=10, padx=20, fill=tk.X)
        
        tk.Label(folder_frame, text="📁 مجلد الحفظ:", 
                font=("Arial", 10, "bold"), bg="#FFF8DC").pack(anchor=tk.W, padx=10, pady=(10, 0))
        
        folder_info_frame = tk.Frame(folder_frame, bg="#FFF8DC")
        folder_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.folder_label = tk.Label(folder_info_frame, text=self.download_path, 
                                    font=("Arial", 9), bg="#FFF8DC", anchor=tk.W)
        self.folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        change_folder_btn = tk.Button(folder_info_frame, text="تغيير", 
                                     command=self.change_folder,
                                     font=("Arial", 9), bg="#FFC107", 
                                     width=8, relief=tk.RAISED, bd=2)
        change_folder_btn.pack(side=tk.RIGHT)
        
        # شريط الحالة
        self.status_label = tk.Label(self.root, text="جاهز - الصق رابط الفيديو أعلاه", 
                                    font=("Arial", 11), bg="#F0F8FF", fg="#2E86AB")
        self.status_label.pack(pady=10)
        
        # ربط Enter بالتحميل
        self.url_entry.bind('<Return>', lambda e: self.download("video"))
        self.url_entry.bind('<Control-v>', lambda e: self.root.after(10, self.paste_url))
    
    def paste_url(self):
        """لصق الرابط"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                self.url_var.set(clipboard_content.strip())
                self.status_label.config(text="✅ تم لصق الرابط - اختر نوع التحميل", fg="green")
            else:
                messagebox.showwarning("تحذير", "الحافظة فارغة")
        except:
            messagebox.showwarning("تحذير", "لا يمكن الوصول للحافظة")
    
    def clear_url(self):
        """مسح الرابط"""
        self.url_var.set("")
        self.status_label.config(text="تم مسح الرابط", fg="orange")
        self.url_entry.focus()
    
    def change_folder(self):
        """تغيير مجلد الحفظ"""
        folder = filedialog.askdirectory(initialdir=self.download_path)
        if folder:
            self.download_path = folder
            self.folder_label.config(text=self.download_path)
            self.status_label.config(text="✅ تم تغيير مجلد الحفظ", fg="green")
    
    def download(self, download_type):
        """بدء التحميل"""
        if self.is_downloading:
            messagebox.showwarning("تحذير", "يوجد تحميل جاري بالفعل")
            return
        
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الفيديو أولاً")
            self.url_entry.focus()
            return
        
        if "youtube.com" not in url and "youtu.be" not in url:
            messagebox.showerror("خطأ", "هذا ليس رابط YouTube صحيح")
            self.url_entry.focus()
            return
        
        if not YT_DLP_AVAILABLE:
            result = messagebox.askyesno("مكتبة مفقودة", 
                                       "yt-dlp غير مثبت.\n\nهل تريد تثبيته الآن؟")
            if result:
                self.install_yt_dlp()
            return
        
        # بدء التحميل
        self.is_downloading = True
        self.status_label.config(text="🔄 جاري التحميل...", fg="blue")
        
        def download_thread():
            try:
                # إعداد yt-dlp
                if download_type == "video":
                    opts = {
                        'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
                        'format': 'best[height<=1080]',
                    }
                    self.status_label.config(text="🎥 جاري تحميل الفيديو...", fg="blue")
                else:  # audio
                    opts = {
                        'outtmpl': f'{self.download_path}/%(title)s.%(ext)s',
                        'format': 'bestaudio/best',
                        'postprocessors': [{
                            'key': 'FFmpegExtractAudio',
                            'preferredcodec': 'mp3',
                            'preferredquality': '192',
                        }],
                    }
                    self.status_label.config(text="🎵 جاري تحميل الصوت...", fg="blue")
                
                # التحميل
                with yt_dlp.YoutubeDL(opts) as ydl:
                    # الحصول على معلومات الفيديو أولاً
                    info = ydl.extract_info(url, download=False)
                    title = info.get('title', 'فيديو غير معروف')
                    
                    # تأكيد التحميل
                    result = messagebox.askyesno("تأكيد التحميل", 
                                               f"هل تريد تحميل:\n\n{title}")
                    if not result:
                        self.status_label.config(text="تم إلغاء التحميل", fg="orange")
                        return
                    
                    # بدء التحميل الفعلي
                    ydl.download([url])
                
                # نجح التحميل
                self.status_label.config(text="✅ تم التحميل بنجاح!", fg="green")
                
                # عرض رسالة نجاح مع خيار فتح المجلد
                result = messagebox.askyesno("نجح التحميل", 
                                           f"✅ تم التحميل بنجاح!\n\nهل تريد فتح مجلد التحميل؟")
                if result:
                    self.open_folder()
                
            except Exception as e:
                self.status_label.config(text="❌ فشل التحميل", fg="red")
                messagebox.showerror("خطأ في التحميل", f"فشل التحميل:\n\n{str(e)}")
            
            finally:
                self.is_downloading = False
        
        # تشغيل التحميل في thread منفصل
        threading.Thread(target=download_thread, daemon=True).start()
    
    def install_yt_dlp(self):
        """تثبيت yt-dlp"""
        def install():
            try:
                import subprocess
                import sys
                
                self.status_label.config(text="🔧 جاري تثبيت yt-dlp...", fg="blue")
                
                subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
                
                self.status_label.config(text="✅ تم تثبيت yt-dlp بنجاح!", fg="green")
                messagebox.showinfo("نجح التثبيت", 
                                  "تم تثبيت yt-dlp بنجاح!\n\nأعد تشغيل البرنامج لاستخدامه.")
                
            except Exception as e:
                self.status_label.config(text="❌ فشل التثبيت", fg="red")
                messagebox.showerror("خطأ في التثبيت", f"فشل في تثبيت yt-dlp:\n\n{str(e)}")
        
        threading.Thread(target=install, daemon=True).start()
    
    def open_folder(self):
        """فتح مجلد التحميل"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.download_path)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{self.download_path}"')  # macOS
                os.system(f'xdg-open "{self.download_path}"')  # Linux
        except Exception as e:
            messagebox.showerror("خطأ", f"لا يمكن فتح المجلد:\n{str(e)}")


def main():
    """تشغيل الواجهة"""
    root = tk.Tk()
    
    # تحسين المظهر
    try:
        root.tk.call('tk', 'scaling', 1.1)
    except:
        pass
    
    app = SimpleYouTubeGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()
