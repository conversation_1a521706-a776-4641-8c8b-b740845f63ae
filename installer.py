#!/usr/bin/env python3
"""
أداة تثبيت YouTube Video Downloader
تثبيت احترافي مع إنشاء اختصارات وإضافة لقائمة ابدأ
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

class YouTubeDownloaderInstaller:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("YouTube Video Downloader - Installer")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # متغيرات التثبيت
        self.install_path = tk.StringVar()
        self.create_desktop_shortcut = tk.BooleanVar(value=True)
        self.create_start_menu = tk.BooleanVar(value=True)
        self.add_to_path = tk.BooleanVar(value=False)
        self.auto_start = tk.BooleanVar(value=False)
        
        # تعيين مسار التثبيت الافتراضي
        default_path = Path.home() / "AppData" / "Local" / "YouTube Video Downloader"
        self.install_path.set(str(default_path))
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2E86AB", height=80)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🎥 YouTube Video Downloader\nInstaller", 
                              font=("Arial", 16, "bold"), 
                              fg="white", bg="#2E86AB")
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, padx=20, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # رسالة الترحيب
        welcome_text = """مرحباً بك في أداة تثبيت YouTube Video Downloader!

هذا البرنامج سيقوم بتثبيت أداة تحميل فيديوهات YouTube على جهازك
مع إنشاء الاختصارات اللازمة وإعداد البرنامج للاستخدام."""
        
        welcome_label = tk.Label(main_frame, text=welcome_text, 
                                justify=tk.LEFT, wraplength=550)
        welcome_label.pack(pady=(0, 20))
        
        # مسار التثبيت
        path_frame = tk.LabelFrame(main_frame, text="مسار التثبيت", padx=10, pady=10)
        path_frame.pack(fill=tk.X, pady=(0, 15))
        
        path_entry = tk.Entry(path_frame, textvariable=self.install_path, 
                             font=("Arial", 10), width=60)
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        browse_btn = tk.Button(path_frame, text="تصفح...", 
                              command=self.browse_install_path)
        browse_btn.pack(side=tk.RIGHT)
        
        # خيارات التثبيت
        options_frame = tk.LabelFrame(main_frame, text="خيارات التثبيت", padx=10, pady=10)
        options_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Checkbutton(options_frame, text="إنشاء اختصار على سطح المكتب", 
                      variable=self.create_desktop_shortcut).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(options_frame, text="إضافة إلى قائمة ابدأ", 
                      variable=self.create_start_menu).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(options_frame, text="إضافة إلى متغير PATH (للاستخدام من سطر الأوامر)", 
                      variable=self.add_to_path).pack(anchor=tk.W, pady=2)
        
        tk.Checkbutton(options_frame, text="تشغيل البرنامج بعد التثبيت", 
                      variable=self.auto_start).pack(anchor=tk.W, pady=2)
        
        # معلومات التثبيت
        info_frame = tk.LabelFrame(main_frame, text="معلومات التثبيت", padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        info_text = """المساحة المطلوبة: ~50 MB
الملفات المضمنة: البرنامج الرئيسي، الشعارات، دليل المستخدم
التوافق: Windows 7/8/10/11 (64-bit)"""
        
        tk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        install_btn = tk.Button(buttons_frame, text="تثبيت", 
                               command=self.start_installation,
                               bg="#4CAF50", fg="white", 
                               font=("Arial", 12, "bold"),
                               padx=30, pady=10)
        install_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", 
                              command=self.root.quit,
                              padx=30, pady=10)
        cancel_btn.pack(side=tk.RIGHT)
        
        # شريط التقدم (مخفي في البداية)
        self.progress_frame = tk.Frame(main_frame)
        self.progress_bar = ttk.Progressbar(self.progress_frame, 
                                           mode='determinate', length=400)
        self.status_label = tk.Label(self.progress_frame, text="")
        
    def browse_install_path(self):
        """تصفح مسار التثبيت"""
        path = filedialog.askdirectory(initialdir=self.install_path.get())
        if path:
            self.install_path.set(path)
    
    def update_progress(self, value, status=""):
        """تحديث شريط التقدم"""
        self.progress_bar['value'] = value
        if status:
            self.status_label.config(text=status)
        self.root.update()
    
    def start_installation(self):
        """بدء عملية التثبيت"""
        install_dir = Path(self.install_path.get())
        
        # التحقق من صحة المسار
        if not install_dir.parent.exists():
            messagebox.showerror("خطأ", "المسار المحدد غير صحيح")
            return
        
        # التأكيد من المستخدم
        if install_dir.exists() and any(install_dir.iterdir()):
            result = messagebox.askyesno("تأكيد", 
                                       f"المجلد موجود ويحتوي على ملفات:\n{install_dir}\n\nهل تريد المتابعة؟")
            if not result:
                return
        
        # إظهار شريط التقدم
        self.progress_frame.pack(fill=tk.X, pady=(20, 0))
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        self.status_label.pack()
        
        try:
            self.perform_installation(install_dir)
            messagebox.showinfo("نجح التثبيت", 
                              "تم تثبيت YouTube Video Downloader بنجاح!\n\n" +
                              f"مسار التثبيت: {install_dir}")
            
            if self.auto_start.get():
                self.launch_program(install_dir)
                
        except Exception as e:
            messagebox.showerror("خطأ في التثبيت", f"فشل التثبيت:\n{str(e)}")
        
        self.root.quit()
    
    def perform_installation(self, install_dir):
        """تنفيذ عملية التثبيت"""
        
        # الخطوة 1: إنشاء مجلد التثبيت
        self.update_progress(10, "إنشاء مجلد التثبيت...")
        install_dir.mkdir(parents=True, exist_ok=True)
        
        # الخطوة 2: نسخ الملفات
        self.update_progress(20, "نسخ الملفات...")
        
        # نسخ الملف التنفيذي
        exe_source = Path("dist/YouTube_Video_Downloader.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, install_dir / "YouTube_Video_Downloader.exe")
        else:
            raise FileNotFoundError("الملف التنفيذي غير موجود")
        
        # نسخ الملفات الإضافية
        additional_files = [
            ("dist/README.txt", "README.txt"),
            ("youtube_downloader_icon.png", "icon.png"),
            ("youtube_downloader_banner.png", "banner.png"),
            ("FINAL_SUMMARY.md", "CHANGELOG.md")
        ]
        
        for source, dest in additional_files:
            if Path(source).exists():
                shutil.copy2(source, install_dir / dest)
        
        self.update_progress(40, "إنشاء ملفات التكوين...")
        
        # إنشاء ملف إلغاء التثبيت
        self.create_uninstaller(install_dir)
        
        # الخطوة 3: إنشاء اختصار سطح المكتب
        if self.create_desktop_shortcut.get():
            self.update_progress(60, "إنشاء اختصار سطح المكتب...")
            self.create_desktop_shortcut_file(install_dir)
        
        # الخطوة 4: إضافة إلى قائمة ابدأ
        if self.create_start_menu.get():
            self.update_progress(70, "إضافة إلى قائمة ابدأ...")
            self.create_start_menu_entry(install_dir)
        
        # الخطوة 5: إضافة إلى PATH
        if self.add_to_path.get():
            self.update_progress(80, "إضافة إلى متغير PATH...")
            self.add_to_system_path(install_dir)
        
        # الخطوة 6: تسجيل البرنامج في النظام
        self.update_progress(90, "تسجيل البرنامج...")
        self.register_program(install_dir)
        
        self.update_progress(100, "تم التثبيت بنجاح!")
    
    def create_uninstaller(self, install_dir):
        """إنشاء أداة إلغاء التثبيت"""
        uninstaller_content = f'''@echo off
title إلغاء تثبيت YouTube Video Downloader

echo جاري إلغاء تثبيت YouTube Video Downloader...

REM حذف الاختصارات
del "%USERPROFILE%\\Desktop\\YouTube Video Downloader.lnk" 2>nul
rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\YouTube Video Downloader" 2>nul

REM حذف ملفات البرنامج
cd /d "{install_dir.parent}"
rmdir /s /q "{install_dir.name}"

echo تم إلغاء التثبيت بنجاح!
pause
'''
        
        uninstaller_path = install_dir / "uninstall.bat"
        with open(uninstaller_path, 'w', encoding='utf-8') as f:
            f.write(uninstaller_content)
    
    def create_desktop_shortcut_file(self, install_dir):
        """إنشاء اختصار على سطح المكتب"""
        try:
            # محاولة استخدام مكتبات Windows
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            shortcut_path = Path(desktop) / "YouTube Video Downloader.lnk"
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = str(install_dir / "YouTube_Video_Downloader.exe")
            shortcut.WorkingDirectory = str(install_dir)
            shortcut.Description = "أداة تحميل فيديوهات YouTube"
            
            icon_path = install_dir / "icon.png"
            if icon_path.exists():
                shortcut.IconLocation = str(icon_path)
            
            shortcut.save()
            
        except ImportError:
            # إنشاء ملف batch كبديل
            desktop_path = Path.home() / "Desktop"
            batch_content = f'''@echo off
cd /d "{install_dir}"
start "" "YouTube_Video_Downloader.exe"
'''
            batch_path = desktop_path / "YouTube Video Downloader.bat"
            with open(batch_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
    
    def create_start_menu_entry(self, install_dir):
        """إضافة إلى قائمة ابدأ"""
        try:
            start_menu = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs"
            app_folder = start_menu / "YouTube Video Downloader"
            app_folder.mkdir(exist_ok=True)
            
            # اختصار البرنامج الرئيسي
            main_shortcut = f'''@echo off
cd /d "{install_dir}"
start "" "YouTube_Video_Downloader.exe"
'''
            with open(app_folder / "YouTube Video Downloader.bat", 'w', encoding='utf-8') as f:
                f.write(main_shortcut)
            
            # اختصار إلغاء التثبيت
            uninstall_shortcut = f'''@echo off
cd /d "{install_dir}"
call "uninstall.bat"
'''
            with open(app_folder / "إلغاء التثبيت.bat", 'w', encoding='utf-8') as f:
                f.write(uninstall_shortcut)
                
        except Exception as e:
            print(f"فشل في إنشاء قائمة ابدأ: {e}")
    
    def add_to_system_path(self, install_dir):
        """إضافة إلى متغير PATH"""
        try:
            # هذا يتطلب صلاحيات مدير، لذا سنتخطاه في الوضع العادي
            pass
        except Exception as e:
            print(f"فشل في إضافة PATH: {e}")
    
    def register_program(self, install_dir):
        """تسجيل البرنامج في النظام"""
        try:
            # إنشاء ملف معلومات البرنامج
            info_content = f"""[Program Info]
Name=YouTube Video Downloader
Version=1.0
InstallPath={install_dir}
InstallDate={Path().stat().st_mtime}
Size=50MB
Publisher=Open Source
"""
            
            info_path = install_dir / "program_info.ini"
            with open(info_path, 'w', encoding='utf-8') as f:
                f.write(info_content)
                
        except Exception as e:
            print(f"فشل في تسجيل البرنامج: {e}")
    
    def launch_program(self, install_dir):
        """تشغيل البرنامج بعد التثبيت"""
        try:
            exe_path = install_dir / "YouTube_Video_Downloader.exe"
            if exe_path.exists():
                subprocess.Popen([str(exe_path)], cwd=str(install_dir))
        except Exception as e:
            print(f"فشل في تشغيل البرنامج: {e}")
    
    def run(self):
        """تشغيل أداة التثبيت"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    
    # التحقق من وجود الملف التنفيذي
    if not Path("dist/YouTube_Video_Downloader.exe").exists():
        messagebox.showerror("خطأ", 
                           "الملف التنفيذي غير موجود!\n\n" +
                           "تأكد من بناء البرنامج أولاً باستخدام:\n" +
                           "بناء_exe_بسيط.bat")
        return
    
    # تشغيل أداة التثبيت
    installer = YouTubeDownloaderInstaller()
    installer.run()

if __name__ == "__main__":
    main()
