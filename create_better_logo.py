#!/usr/bin/env python3
"""
إنشاء شعار محسّن لأداة تحميل YouTube
شعار أكثر وضوحاً ووضوحاً للاختصارات
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_high_quality_icon():
    """إنشاء أيقونة عالية الجودة"""
    
    # أبعاد الأيقونة
    size = 256  # حجم أكبر للوضوح
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان YouTube المحسّنة
    youtube_red = (255, 0, 0)
    youtube_dark_red = (204, 0, 0)
    white = (255, 255, 255)
    black = (0, 0, 0)
    shadow_color = (128, 128, 128, 100)
    
    # رسم ظل خفيف
    shadow_offset = 8
    draw.ellipse([shadow_offset, shadow_offset, size-4+shadow_offset, size-4+shadow_offset], 
                 fill=shadow_color)
    
    # رسم الخلفية الدائرية الحمراء
    margin = 16
    draw.ellipse([margin, margin, size-margin, size-margin], 
                 fill=youtube_red, outline=youtube_dark_red, width=4)
    
    # رسم مثلث التشغيل الأبيض (أكبر وأوضح)
    triangle_size = size // 4
    center_x, center_y = size // 2, size // 2
    
    # نقاط المثلث (محسّنة للوضوح)
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2 + 8, center_y)  # إزاحة قليلة لليمين
    ]
    
    # رسم ظل للمثلث
    shadow_triangle = [(x+2, y+2) for x, y in triangle_points]
    draw.polygon(shadow_triangle, fill=(0, 0, 0, 50))
    
    # رسم المثلث الأبيض
    draw.polygon(triangle_points, fill=white, outline=black, width=2)
    
    # رسم سهم التحميل في الزاوية السفلية اليمنى
    arrow_size = 40
    arrow_x = size - arrow_size - 20
    arrow_y = size - arrow_size - 20
    
    # خلفية دائرية للسهم
    draw.ellipse([arrow_x-5, arrow_y-5, arrow_x+arrow_size+5, arrow_y+arrow_size+5], 
                 fill=white, outline=youtube_dark_red, width=2)
    
    # رسم سهم التحميل
    arrow_center_x = arrow_x + arrow_size // 2
    arrow_center_y = arrow_y + arrow_size // 2
    
    # خط السهم العمودي
    draw.rectangle([arrow_center_x-3, arrow_y+8, arrow_center_x+3, arrow_y+arrow_size-8], 
                   fill=youtube_red)
    
    # رأس السهم
    arrow_head = [
        (arrow_center_x, arrow_y + arrow_size - 8),
        (arrow_center_x - 8, arrow_y + arrow_size - 16),
        (arrow_center_x + 8, arrow_y + arrow_size - 16)
    ]
    draw.polygon(arrow_head, fill=youtube_red)
    
    # حفظ بأحجام متعددة
    sizes = [16, 24, 32, 48, 64, 128, 256]
    
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        resized.save(f'youtube_icon_{icon_size}.png', 'PNG')
    
    # حفظ الأيقونة الرئيسية
    img.save('youtube_downloader_icon_hq.png', 'PNG')
    
    print("✅ تم إنشاء الأيقونات عالية الجودة:")
    for icon_size in sizes:
        print(f"   - youtube_icon_{icon_size}.png")
    print(f"   - youtube_downloader_icon_hq.png")
    
    return img

def create_windows_ico():
    """إنشاء ملف ICO للويندوز"""
    try:
        # قائمة الأحجام المطلوبة لملف ICO
        ico_sizes = [16, 24, 32, 48, 64, 128, 256]
        images = []
        
        for size in ico_sizes:
            if os.path.exists(f'youtube_icon_{size}.png'):
                img = Image.open(f'youtube_icon_{size}.png')
                images.append(img)
        
        if images:
            # حفظ كملف ICO
            images[0].save('youtube_downloader_hq.ico', format='ICO', 
                          sizes=[(img.width, img.height) for img in images],
                          append_images=images[1:])
            
            print("✅ تم إنشاء ملف ICO: youtube_downloader_hq.ico")
            return True
        
    except Exception as e:
        print(f"⚠️ فشل في إنشاء ملف ICO: {e}")
        return False

def create_taskbar_icon():
    """إنشاء أيقونة مخصصة لشريط المهام"""
    
    # حجم مناسب لشريط المهام
    size = 64
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان
    youtube_red = (255, 0, 0)
    white = (255, 255, 255)
    black = (0, 0, 0)
    
    # رسم مربع أحمر مع زوايا مدورة
    margin = 4
    draw.rounded_rectangle([margin, margin, size-margin, size-margin], 
                          radius=8, fill=youtube_red, outline=black, width=2)
    
    # رسم مثلث التشغيل
    triangle_size = 20
    center_x, center_y = size // 2, size // 2
    
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    draw.polygon(triangle_points, fill=white, outline=black, width=1)
    
    # رسم سهم صغير
    arrow_x = size - 16
    arrow_y = size - 16
    
    # سهم للأسفل
    draw.polygon([
        (arrow_x, arrow_y - 4),
        (arrow_x - 3, arrow_y - 1),
        (arrow_x - 1, arrow_y - 1),
        (arrow_x - 1, arrow_y + 2),
        (arrow_x + 1, arrow_y + 2),
        (arrow_x + 1, arrow_y - 1),
        (arrow_x + 3, arrow_y - 1)
    ], fill=white, outline=black, width=1)
    
    # حفظ الأيقونة
    img.save('youtube_taskbar_icon.png', 'PNG')
    print("✅ تم إنشاء أيقونة شريط المهام: youtube_taskbar_icon.png")
    
    return img

def create_desktop_shortcut_icon():
    """إنشاء أيقونة مخصصة للاختصار على سطح المكتب"""
    
    # حجم مناسب لسطح المكتب
    size = 128
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان متدرجة
    youtube_red = (255, 0, 0)
    youtube_light_red = (255, 100, 100)
    white = (255, 255, 255)
    black = (0, 0, 0)
    
    # رسم خلفية متدرجة
    for i in range(size):
        alpha = int(255 * (1 - i / size * 0.3))
        color = (*youtube_red, alpha)
        draw.ellipse([i//4, i//4, size-i//4, size-i//4], fill=color)
    
    # رسم الدائرة الرئيسية
    margin = 8
    draw.ellipse([margin, margin, size-margin, size-margin], 
                 fill=youtube_red, outline=black, width=3)
    
    # رسم مثلث التشغيل مع تأثير ثلاثي الأبعاد
    triangle_size = 32
    center_x, center_y = size // 2, size // 2
    
    # ظل المثلث
    shadow_triangle = [
        (center_x - triangle_size//2 + 2, center_y - triangle_size//2 + 2),
        (center_x - triangle_size//2 + 2, center_y + triangle_size//2 + 2),
        (center_x + triangle_size//2 + 2, center_y + 2)
    ]
    draw.polygon(shadow_triangle, fill=(0, 0, 0, 100))
    
    # المثلث الرئيسي
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    draw.polygon(triangle_points, fill=white, outline=black, width=2)
    
    # رسم سهم التحميل مع خلفية
    arrow_size = 24
    arrow_x = size - arrow_size - 12
    arrow_y = size - arrow_size - 12
    
    # خلفية السهم
    draw.ellipse([arrow_x-4, arrow_y-4, arrow_x+arrow_size+4, arrow_y+arrow_size+4], 
                 fill=white, outline=black, width=2)
    
    # السهم
    arrow_center_x = arrow_x + arrow_size // 2
    arrow_center_y = arrow_y + arrow_size // 2
    
    # خط السهم
    draw.rectangle([arrow_center_x-2, arrow_y+4, arrow_center_x+2, arrow_y+arrow_size-6], 
                   fill=youtube_red)
    
    # رأس السهم
    arrow_head = [
        (arrow_center_x, arrow_y + arrow_size - 6),
        (arrow_center_x - 6, arrow_y + arrow_size - 12),
        (arrow_center_x + 6, arrow_y + arrow_size - 12)
    ]
    draw.polygon(arrow_head, fill=youtube_red)
    
    # حفظ الأيقونة
    img.save('youtube_desktop_icon.png', 'PNG')
    print("✅ تم إنشاء أيقونة سطح المكتب: youtube_desktop_icon.png")
    
    return img

def create_app_logo():
    """إنشاء شعار التطبيق الرئيسي"""
    
    # حجم كبير للشعار
    width, height = 512, 512
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان
    youtube_red = (255, 0, 0)
    youtube_dark_red = (204, 0, 0)
    white = (255, 255, 255)
    black = (0, 0, 0)
    
    # رسم خلفية متدرجة
    center_x, center_y = width // 2, height // 2
    max_radius = min(width, height) // 2 - 20
    
    for radius in range(max_radius, 0, -5):
        alpha = int(255 * (radius / max_radius))
        color = (*youtube_red, alpha)
        draw.ellipse([center_x - radius, center_y - radius, 
                     center_x + radius, center_y + radius], fill=color)
    
    # رسم الدائرة الرئيسية
    margin = 40
    draw.ellipse([margin, margin, width-margin, height-margin], 
                 fill=youtube_red, outline=youtube_dark_red, width=8)
    
    # رسم مثلث التشغيل الكبير
    triangle_size = 120
    
    triangle_points = [
        (center_x - triangle_size//2, center_y - triangle_size//2),
        (center_x - triangle_size//2, center_y + triangle_size//2),
        (center_x + triangle_size//2 + 20, center_y)
    ]
    
    # ظل المثلث
    shadow_triangle = [(x+5, y+5) for x, y in triangle_points]
    draw.polygon(shadow_triangle, fill=(0, 0, 0, 100))
    
    # المثلث الرئيسي
    draw.polygon(triangle_points, fill=white, outline=black, width=4)
    
    # رسم سهم التحميل الكبير
    arrow_size = 80
    arrow_x = width - arrow_size - 60
    arrow_y = height - arrow_size - 60
    
    # خلفية السهم
    draw.ellipse([arrow_x-10, arrow_y-10, arrow_x+arrow_size+10, arrow_y+arrow_size+10], 
                 fill=white, outline=youtube_dark_red, width=4)
    
    # السهم
    arrow_center_x = arrow_x + arrow_size // 2
    arrow_center_y = arrow_y + arrow_size // 2
    
    # خط السهم
    draw.rectangle([arrow_center_x-6, arrow_y+15, arrow_center_x+6, arrow_y+arrow_size-15], 
                   fill=youtube_red)
    
    # رأس السهم
    arrow_head = [
        (arrow_center_x, arrow_y + arrow_size - 15),
        (arrow_center_x - 15, arrow_y + arrow_size - 30),
        (arrow_center_x + 15, arrow_y + arrow_size - 30)
    ]
    draw.polygon(arrow_head, fill=youtube_red)
    
    # حفظ الشعار
    img.save('youtube_app_logo.png', 'PNG')
    print("✅ تم إنشاء شعار التطبيق: youtube_app_logo.png")
    
    return img

def main():
    """إنشاء جميع الشعارات المحسّنة"""
    print("🎨 إنشاء شعارات محسّنة لأداة تحميل YouTube...")
    print("=" * 60)
    
    try:
        # إنشاء الأيقونات عالية الجودة
        print("\n1. إنشاء الأيقونات عالية الجودة...")
        create_high_quality_icon()
        
        # إنشاء ملف ICO
        print("\n2. إنشاء ملف ICO للويندوز...")
        create_windows_ico()
        
        # إنشاء أيقونة شريط المهام
        print("\n3. إنشاء أيقونة شريط المهام...")
        create_taskbar_icon()
        
        # إنشاء أيقونة سطح المكتب
        print("\n4. إنشاء أيقونة سطح المكتب...")
        create_desktop_shortcut_icon()
        
        # إنشاء شعار التطبيق
        print("\n5. إنشاء شعار التطبيق...")
        create_app_logo()
        
        print("\n🎉 تم إنشاء جميع الشعارات المحسّنة بنجاح!")
        print("\n📁 الملفات المنشأة:")
        print("   - youtube_downloader_icon_hq.png (أيقونة عالية الجودة)")
        print("   - youtube_downloader_hq.ico (ملف ICO للويندوز)")
        print("   - youtube_taskbar_icon.png (أيقونة شريط المهام)")
        print("   - youtube_desktop_icon.png (أيقونة سطح المكتب)")
        print("   - youtube_app_logo.png (شعار التطبيق)")
        print("   - youtube_icon_[16-256].png (أحجام متعددة)")
        
        print("\n💡 لاستخدام الشعارات الجديدة:")
        print("1. استبدل الأيقونات القديمة بالجديدة")
        print("2. أعد بناء الملف التنفيذي")
        print("3. أعد إنشاء الاختصارات")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الشعارات: {e}")

if __name__ == "__main__":
    main()
