@echo off
chcp 65001 >nul
title YouTube Video Downloader - الواجهة الأصلية المحسّنة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎥 YouTube Video Downloader                   ║
echo ║                   الواجهة الأصلية المحسّنة                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 التحقق من المكتبات المطلوبة...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من yt-dlp
python -c "import yt_dlp" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  yt-dlp غير مثبت - سيتم تثبيته تلقائياً
    echo.
    echo 📦 جاري تثبيت yt-dlp...
    pip install yt-dlp
    if errorlevel 1 (
        echo ❌ فشل في تثبيت yt-dlp
        echo جرب: pip install --user yt-dlp
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت yt-dlp بنجاح
) else (
    echo ✅ yt-dlp متوفر
)

echo.
echo 🚀 تشغيل الواجهة الأصلية المحسّنة...
echo.

REM تشغيل الواجهة
python gui.py

echo.
echo تم إغلاق الواجهة
pause
