@echo off
chcp 65001 >nul
title حل مشكلة HTTP 400 - أداة تحميل YouTube

echo.
echo ========================================
echo     حل مشكلة HTTP 400 Bad Request
echo ========================================
echo.

echo 🔧 جاري حل المشكلة...
echo.

echo 1. تحديث pytube...
pip install --upgrade pytube
echo.

echo 2. تثبيت yt-dlp (البديل الأفضل)...
pip install yt-dlp
echo.

echo 3. تثبيت المكتبات المساعدة...
pip install colorama
echo.

echo ✅ تم حل المشكلة!
echo.
echo الآن يمكنك استخدام:
echo.
echo 🎯 الأداة المضمونة: python تحميل_مضمون.py
echo 🔧 الأداة المحسنة: python fixed_downloader.py
echo 📱 الواجهة البسيطة: python simple_downloader.py
echo.

echo هل تريد تشغيل الأداة المضمونة الآن؟ (y/n)
set /p choice="اختر: "

if /i "%choice%"=="y" (
    echo.
    echo 🚀 تشغيل الأداة المضمونة...
    python تحميل_مضمون.py
) else (
    echo.
    echo يمكنك تشغيل أي أداة متى شئت!
)

echo.
pause
