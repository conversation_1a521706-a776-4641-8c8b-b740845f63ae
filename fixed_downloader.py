#!/usr/bin/env python3
"""
أداة تحميل YouTube محسّنة مع معالجة أخطاء HTTP 400
حل مشكلة "Error getting video info: HTTP Error 400: Bad Request"
"""

import os
import re
import time
import requests
from pathlib import Path
from typing import List, Dict, Optional, Callable
from urllib.parse import urlparse, parse_qs
from colorama import init, Fore, Style

# تهيئة الألوان
init(autoreset=True)

# محاولة استيراد pytube مع معالجة الأخطاء
try:
    from pytube import YouTube, Playlist
    from pytube.exceptions import RegexMatchError, VideoUnavailable
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False
    print(f"{Fore.YELLOW}تحذير: pytube غير متوفر. سيتم استخدام طريقة بديلة.{Style.RESET_ALL}")

# محاولة استيراد yt-dlp كبديل
try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False


class FixedYouTubeDownloader:
    """أداة تحميل محسّنة مع معالجة الأخطاء"""
    
    def __init__(self, download_path: str = "تحميلات_YouTube"):
        self.download_path = Path(download_path)
        self.download_path.mkdir(exist_ok=True)
        
        # إعدادات yt-dlp
        self.ydl_opts_video = {
            'outtmpl': str(self.download_path / '%(title)s.%(ext)s'),
            'format': 'best[height<=1080]',
            'noplaylist': True,
        }
        
        self.ydl_opts_audio = {
            'outtmpl': str(self.download_path / '%(title)s.%(ext)s'),
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'noplaylist': True,
        }
    
    def sanitize_filename(self, filename: str) -> str:
        """تنظيف اسم الملف"""
        invalid_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(invalid_chars, '_', filename)
        return sanitized[:200] if len(sanitized) > 200 else sanitized
    
    def extract_video_id(self, url: str) -> str:
        """استخراج معرف الفيديو من الرابط"""
        patterns = [
            r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',
            r'(?:embed\/)([0-9A-Za-z_-]{11})',
            r'(?:watch\?v=)([0-9A-Za-z_-]{11})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def get_video_info_ydl(self, url: str) -> Dict:
        """الحصول على معلومات الفيديو باستخدام yt-dlp"""
        if not YT_DLP_AVAILABLE:
            raise Exception("yt-dlp غير متوفر. قم بتثبيته: pip install yt-dlp")
        
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'title': info.get('title', 'غير معروف'),
                    'author': info.get('uploader', 'غير معروف'),
                    'length': info.get('duration', 0),
                    'views': info.get('view_count', 0),
                    'description': info.get('description', '')[:200] + "...",
                    'video_id': info.get('id', ''),
                    'thumbnail_url': info.get('thumbnail', '')
                }
        except Exception as e:
            raise Exception(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
    
    def get_video_info_pytube(self, url: str) -> Dict:
        """الحصول على معلومات الفيديو باستخدام pytube مع معالجة محسّنة"""
        if not PYTUBE_AVAILABLE:
            raise Exception("pytube غير متوفر")
        
        try:
            # محاولة مع إعدادات مختلفة
            for attempt in range(3):
                try:
                    # إضافة تأخير بين المحاولات
                    if attempt > 0:
                        time.sleep(2)
                    
                    yt = YouTube(url, use_oauth=False, allow_oauth_cache=False)
                    
                    return {
                        'title': yt.title or 'غير معروف',
                        'author': yt.author or 'غير معروف',
                        'length': yt.length or 0,
                        'views': yt.views or 0,
                        'description': (yt.description or '')[:200] + "...",
                        'video_id': yt.video_id or '',
                        'thumbnail_url': yt.thumbnail_url or ''
                    }
                except Exception as e:
                    if attempt == 2:  # المحاولة الأخيرة
                        raise e
                    continue
                    
        except Exception as e:
            raise Exception(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
    
    def get_video_info(self, url: str) -> Dict:
        """الحصول على معلومات الفيديو مع تجربة طرق متعددة"""
        print(f"{Fore.CYAN}🔍 جاري الحصول على معلومات الفيديو...{Style.RESET_ALL}")
        
        # تجربة yt-dlp أولاً (أكثر استقراراً)
        if YT_DLP_AVAILABLE:
            try:
                return self.get_video_info_ydl(url)
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ فشل yt-dlp: {str(e)}{Style.RESET_ALL}")
        
        # تجربة pytube كبديل
        if PYTUBE_AVAILABLE:
            try:
                return self.get_video_info_pytube(url)
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ فشل pytube: {str(e)}{Style.RESET_ALL}")
        
        # إذا فشلت كل الطرق
        raise Exception("فشل في الحصول على معلومات الفيديو. تأكد من صحة الرابط واتصال الإنترنت.")
    
    def download_video_ydl(self, url: str, quality: str = "best") -> str:
        """تحميل الفيديو باستخدام yt-dlp"""
        if not YT_DLP_AVAILABLE:
            raise Exception("yt-dlp غير متوفر. قم بتثبيته: pip install yt-dlp")
        
        try:
            # تحديد جودة الفيديو
            if quality == "highest":
                format_selector = "best"
            elif quality == "lowest":
                format_selector = "worst"
            elif quality.endswith('p'):
                height = quality[:-1]
                format_selector = f"best[height<={height}]"
            else:
                format_selector = "best"
            
            opts = self.ydl_opts_video.copy()
            opts['format'] = format_selector
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                # الحصول على معلومات الفيديو أولاً
                info = ydl.extract_info(url, download=False)
                title = self.sanitize_filename(info.get('title', 'video'))
                
                # تحميل الفيديو
                ydl.download([url])
                
                # البحث عن الملف المحمل
                for file in self.download_path.glob(f"{title}*"):
                    if file.is_file():
                        return str(file)
                
                # إذا لم نجد الملف، ابحث عن أحدث ملف
                files = list(self.download_path.glob("*"))
                if files:
                    latest_file = max(files, key=os.path.getctime)
                    return str(latest_file)
                
                raise Exception("لم يتم العثور على الملف المحمل")
                
        except Exception as e:
            raise Exception(f"خطأ في تحميل الفيديو: {str(e)}")
    
    def download_audio_ydl(self, url: str) -> str:
        """تحميل الصوت باستخدام yt-dlp"""
        if not YT_DLP_AVAILABLE:
            raise Exception("yt-dlp غير متوفر. قم بتثبيته: pip install yt-dlp")
        
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts_audio) as ydl:
                # الحصول على معلومات الفيديو أولاً
                info = ydl.extract_info(url, download=False)
                title = self.sanitize_filename(info.get('title', 'audio'))
                
                # تحميل الصوت
                ydl.download([url])
                
                # البحث عن الملف المحمل
                for ext in ['.mp3', '.m4a', '.webm']:
                    audio_file = self.download_path / f"{title}{ext}"
                    if audio_file.exists():
                        return str(audio_file)
                
                # البحث عن أحدث ملف صوتي
                audio_files = []
                for ext in ['.mp3', '.m4a', '.webm']:
                    audio_files.extend(self.download_path.glob(f"*{ext}"))
                
                if audio_files:
                    latest_file = max(audio_files, key=os.path.getctime)
                    return str(latest_file)
                
                raise Exception("لم يتم العثور على الملف الصوتي المحمل")
                
        except Exception as e:
            raise Exception(f"خطأ في تحميل الصوت: {str(e)}")
    
    def download_video(self, url: str, quality: str = "highest") -> str:
        """تحميل الفيديو مع تجربة طرق متعددة"""
        print(f"{Fore.GREEN}⬇️ بدء تحميل الفيديو...{Style.RESET_ALL}")
        
        # تجربة yt-dlp أولاً
        if YT_DLP_AVAILABLE:
            try:
                return self.download_video_ydl(url, quality)
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ فشل yt-dlp: {str(e)}{Style.RESET_ALL}")
        
        # إذا فشلت كل الطرق
        raise Exception("فشل في تحميل الفيديو. جرب تثبيت yt-dlp: pip install yt-dlp")
    
    def download_audio(self, url: str) -> str:
        """تحميل الصوت مع تجربة طرق متعددة"""
        print(f"{Fore.GREEN}🎵 بدء تحميل الصوت...{Style.RESET_ALL}")
        
        # تجربة yt-dlp أولاً
        if YT_DLP_AVAILABLE:
            try:
                return self.download_audio_ydl(url)
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ فشل yt-dlp: {str(e)}{Style.RESET_ALL}")
        
        # إذا فشلت كل الطرق
        raise Exception("فشل في تحميل الصوت. جرب تثبيت yt-dlp: pip install yt-dlp")


def install_yt_dlp():
    """تثبيت yt-dlp"""
    print(f"{Fore.CYAN}🔧 جاري تثبيت yt-dlp...{Style.RESET_ALL}")
    try:
        import subprocess
        import sys
        
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
        print(f"{Fore.GREEN}✅ تم تثبيت yt-dlp بنجاح!{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ فشل في تثبيت yt-dlp: {str(e)}{Style.RESET_ALL}")
        return False


def main():
    """الدالة الرئيسية"""
    print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║              أداة تحميل YouTube المحسّنة                    ║
║                  حل مشكلة HTTP 400                          ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
""")
    
    # التحقق من توفر المكتبات
    if not YT_DLP_AVAILABLE and not PYTUBE_AVAILABLE:
        print(f"{Fore.RED}❌ لا توجد مكتبات تحميل متوفرة{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}هل تريد تثبيت yt-dlp؟ (y/n): {Style.RESET_ALL}", end="")
        if input().lower() in ['y', 'yes', 'نعم', 'ن']:
            if install_yt_dlp():
                print(f"{Fore.GREEN}أعد تشغيل البرنامج الآن{Style.RESET_ALL}")
            return
        else:
            print(f"{Fore.RED}لا يمكن المتابعة بدون مكتبات التحميل{Style.RESET_ALL}")
            return
    
    # إنشاء أداة التحميل
    downloader = FixedYouTubeDownloader()
    
    while True:
        try:
            # إدخال الرابط
            print(f"\n{Fore.YELLOW}📝 أدخل رابط الفيديو من YouTube:{Style.RESET_ALL}")
            url = input(f"{Fore.WHITE}الرابط: {Style.RESET_ALL}").strip()
            
            if not url:
                print(f"{Fore.RED}❌ يرجى إدخال رابط صحيح{Style.RESET_ALL}")
                continue
            
            if "youtube.com" not in url and "youtu.be" not in url:
                print(f"{Fore.RED}❌ هذا ليس رابط YouTube صحيح{Style.RESET_ALL}")
                continue
            
            # عرض معلومات الفيديو
            try:
                info = downloader.get_video_info(url)
                print(f"\n{Fore.CYAN}📹 معلومات الفيديو:{Style.RESET_ALL}")
                print(f"   العنوان: {info['title']}")
                print(f"   القناة: {info['author']}")
                print(f"   المدة: {info['length']} ثانية")
                print(f"   المشاهدات: {info['views']:,}")
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️ لا يمكن عرض معلومات الفيديو: {str(e)}{Style.RESET_ALL}")
            
            # اختيار نوع التحميل
            print(f"\n{Fore.CYAN}📋 اختر نوع التحميل:{Style.RESET_ALL}")
            print("1. 🎥 فيديو")
            print("2. 🎵 صوت فقط")
            
            choice = input(f"\n{Fore.YELLOW}اختر (1-2): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                # اختيار الجودة
                print(f"\n{Fore.CYAN}📺 اختر جودة الفيديو:{Style.RESET_ALL}")
                print("1. أعلى جودة")
                print("2. 1080p")
                print("3. 720p")
                print("4. 480p")
                
                quality_choice = input(f"\n{Fore.YELLOW}اختر (1-4): {Style.RESET_ALL}").strip()
                quality_map = {"1": "highest", "2": "1080p", "3": "720p", "4": "480p"}
                quality = quality_map.get(quality_choice, "highest")
                
                # تحميل الفيديو
                filepath = downloader.download_video(url, quality)
                print(f"\n{Fore.GREEN}✅ تم تحميل الفيديو بنجاح!{Style.RESET_ALL}")
                print(f"   📁 المكان: {filepath}")
                
            elif choice == "2":
                # تحميل الصوت
                filepath = downloader.download_audio(url)
                print(f"\n{Fore.GREEN}✅ تم تحميل الصوت بنجاح!{Style.RESET_ALL}")
                print(f"   📁 المكان: {filepath}")
            
            else:
                print(f"{Fore.RED}❌ اختيار غير صحيح{Style.RESET_ALL}")
                continue
            
            # سؤال عن تحميل آخر
            print(f"\n{Fore.CYAN}هل تريد تحميل فيديو آخر؟{Style.RESET_ALL}")
            another = input(f"{Fore.YELLOW}اكتب 'نعم' للمتابعة، أي شيء آخر للخروج: {Style.RESET_ALL}").strip().lower()
            
            if another not in ['نعم', 'y', 'yes', 'ن']:
                break
            
            print("\n" + "="*60)
            
        except Exception as e:
            print(f"\n{Fore.RED}❌ خطأ: {str(e)}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 جرب:{Style.RESET_ALL}")
            print("   - التأكد من صحة الرابط")
            print("   - فحص اتصال الإنترنت")
            print("   - تثبيت yt-dlp: pip install yt-dlp")
    
    print(f"\n{Fore.GREEN}🙏 شكراً لاستخدام أداة التحميل المحسّنة!{Style.RESET_ALL}")


if __name__ == "__main__":
    main()
