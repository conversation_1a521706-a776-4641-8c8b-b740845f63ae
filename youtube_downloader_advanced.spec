# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين متقدم لبناء YouTube Video Downloader
يتضمن جميع المكتبات والملفات المطلوبة
"""

import os
from pathlib import Path

block_cipher = None

# قائمة الملفات الإضافية (الشعارات والموارد)
added_files = []

# إضافة ملفات الشعارات إذا كانت موجودة
logo_files = [
    'youtube_downloader_icon.png',
    'youtube_downloader_banner.png', 
    'youtube_icon_small.png',
    'ascii_logo.py'
]

for logo_file in logo_files:
    if os.path.exists(logo_file):
        added_files.append((logo_file, '.'))

# إضافة ملفات Python الإضافية
python_files = [
    'downloader.py',
    'ascii_logo.py',
    'create_logo.py'
]

for py_file in python_files:
    if os.path.exists(py_file):
        added_files.append((py_file, '.'))

# المكتبات المخفية المطلوبة
hidden_imports = [
    # yt-dlp ومكتباته
    'yt_dlp',
    'yt_dlp.extractor',
    'yt_dlp.downloader',
    'yt_dlp.postprocessor',
    
    # PIL/Pillow
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # tkinter ومكوناته
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    
    # مكتبات أخرى
    'colorama',
    'tqdm',
    'pathlib',
    'threading',
    'subprocess',
    'json',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'ssl',
    'certifi'
]

# تحليل الملف الرئيسي
a = Analysis(
    ['gui_working.py'],
    pathex=[os.getcwd()],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد مكتبات غير مطلوبة لتقليل الحجم
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# إزالة الملفات المكررة
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YouTube_Video_Downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # ضغط الملف
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='youtube_downloader.ico' if os.path.exists('youtube_downloader.ico') else None,
    version_file=None,
    uac_admin=False,  # لا يتطلب صلاحيات مدير
    uac_uiaccess=False,
)
