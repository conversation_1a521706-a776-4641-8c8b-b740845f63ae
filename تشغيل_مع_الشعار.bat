@echo off
chcp 65001 >nul
title YouTube Video Downloader - مع الشعار الجديد

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎨 YouTube Video Downloader                   ║
echo ║                    مع الشعار الجديد                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎨 إعداد الشعارات والواجهة...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
pip install yt-dlp Pillow colorama >nul 2>&1

REM إنشاء الشعارات
echo 🎨 إنشاء الشعارات...
python create_logo.py

REM التحقق من وجود الشعارات
if exist "youtube_downloader_icon.png" (
    echo ✅ تم إنشاء الشعار الرئيسي
) else (
    echo ⚠️ لم يتم إنشاء الشعار الرئيسي
)

if exist "youtube_downloader_banner.png" (
    echo ✅ تم إنشاء بانر الشعار
) else (
    echo ⚠️ لم يتم إنشاء بانر الشعار
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 اختر الواجهة                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 1. 🖥️ الواجهة الأصلية مع الشعار الجديد
echo 2. 💯 الواجهة التي تعمل 100%% مع الشعار
echo 3. 🧪 اختبار التحميل أولاً
echo 4. 🎨 إعادة إنشاء الشعارات فقط
echo 5. 🚪 خروج
echo.

set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🖥️ تشغيل الواجهة الأصلية مع الشعار...
    python gui.py
) else if "%choice%"=="2" (
    echo.
    echo 💯 تشغيل الواجهة التي تعمل 100%% مع الشعار...
    python gui_working.py
) else if "%choice%"=="3" (
    echo.
    echo 🧪 تشغيل اختبار التحميل...
    python test_download.py
) else if "%choice%"=="4" (
    echo.
    echo 🎨 إعادة إنشاء الشعارات...
    python create_logo.py
    echo.
    echo ✅ تم إعادة إنشاء الشعارات!
    pause
    goto :choice
) else if "%choice%"=="5" (
    echo.
    echo 👋 وداعاً!
    exit
) else (
    echo.
    echo ❌ اختيار غير صحيح
    pause
    goto :choice
)

echo.
echo هل تريد تشغيل واجهة أخرى؟ (y/n)
set /p again="اختر: "

if /i "%again%"=="y" (
    cls
    goto :choice
) else (
    echo.
    echo 🙏 شكراً لاستخدام YouTube Downloader مع الشعار الجديد!
)

:choice
pause
