@echo off
chcp 65001 >nul
title تحديث اختصار سطح المكتب - YouTube Video Downloader

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🎨 تحديث اختصار سطح المكتب                     ║
echo ║              YouTube Video Downloader                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 البحث عن الاختصار الحالي...

REM البحث عن الاختصارات الموجودة
set FOUND_SHORTCUT=0

if exist "%USERPROFILE%\Desktop\YouTube Video Downloader.bat" (
    echo ✅ تم العثور على اختصار batch
    set FOUND_SHORTCUT=1
    set "OLD_SHORTCUT=%USERPROFILE%\Desktop\YouTube Video Downloader.bat"
)

if exist "%USERPROFILE%\Desktop\YouTube Video Downloader.lnk" (
    echo ✅ تم العثور على اختصار Windows
    set FOUND_SHORTCUT=1
    set "OLD_SHORTCUT=%USERPROFILE%\Desktop\YouTube Video Downloader.lnk"
)

if %FOUND_SHORTCUT%==0 (
    echo ⚠️ لم يتم العثور على اختصار موجود
    echo سيتم إنشاء اختصار جديد...
) else (
    echo 📁 الاختصار الحالي: %OLD_SHORTCUT%
)

echo.
echo 🔍 التحقق من الملف التنفيذي...

if not exist "dist\YouTube_Video_Downloader.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo يرجى بناء البرنامج أولاً باستخدام:
    echo    بناء_exe_بسيط.bat
    echo.
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود

REM الحصول على المسار الكامل للملف التنفيذي
for %%A in ("dist\YouTube_Video_Downloader.exe") do set "EXE_PATH=%%~fA"
for %%A in ("dist") do set "EXE_DIR=%%~fA"

echo 📁 مسار البرنامج: %EXE_PATH%

echo.
echo 🔄 إنشاء اختصار محسّن...

REM حذف الاختصار القديم إذا وجد
if %FOUND_SHORTCUT%==1 (
    echo 🗑️ حذف الاختصار القديم...
    del "%OLD_SHORTCUT%" >nul 2>&1
)

REM إنشاء اختصار جديد محسّن
set "NEW_SHORTCUT=%USERPROFILE%\Desktop\YouTube Video Downloader.bat"

echo 🔗 إنشاء اختصار جديد...

REM كتابة محتوى الاختصار الجديد
echo @echo off > "%NEW_SHORTCUT%"
echo chcp 65001 ^>nul >> "%NEW_SHORTCUT%"
echo title YouTube Video Downloader - محسّن >> "%NEW_SHORTCUT%"
echo. >> "%NEW_SHORTCUT%"
echo echo. >> "%NEW_SHORTCUT%"
echo echo ╔══════════════════════════════════════════════════════════════╗ >> "%NEW_SHORTCUT%"
echo echo ║                🎥 YouTube Video Downloader                   ║ >> "%NEW_SHORTCUT%"
echo echo ║                     أداة التحميل المحسّنة                  ║ >> "%NEW_SHORTCUT%"
echo echo ╚══════════════════════════════════════════════════════════════╝ >> "%NEW_SHORTCUT%"
echo echo. >> "%NEW_SHORTCUT%"
echo echo 🚀 تشغيل أداة تحميل YouTube... >> "%NEW_SHORTCUT%"
echo echo. >> "%NEW_SHORTCUT%"
echo. >> "%NEW_SHORTCUT%"
echo cd /d "%EXE_DIR%" >> "%NEW_SHORTCUT%"
echo. >> "%NEW_SHORTCUT%"
echo if exist "YouTube_Video_Downloader.exe" ^( >> "%NEW_SHORTCUT%"
echo     start "" "YouTube_Video_Downloader.exe" >> "%NEW_SHORTCUT%"
echo     echo ✅ تم تشغيل البرنامج بنجاح! >> "%NEW_SHORTCUT%"
echo     timeout /t 2 /nobreak ^>nul >> "%NEW_SHORTCUT%"
echo ^) else ^( >> "%NEW_SHORTCUT%"
echo     echo ❌ الملف التنفيذي غير موجود >> "%NEW_SHORTCUT%"
echo     echo المسار: %EXE_PATH% >> "%NEW_SHORTCUT%"
echo     echo. >> "%NEW_SHORTCUT%"
echo     echo 💡 جرب إعادة بناء البرنامج >> "%NEW_SHORTCUT%"
echo     pause >> "%NEW_SHORTCUT%"
echo ^) >> "%NEW_SHORTCUT%"

echo ✅ تم إنشاء الاختصار الجديد!

echo.
echo 📊 معلومات الاختصار:
echo    📁 المكان: %NEW_SHORTCUT%
echo    🎯 الهدف: %EXE_PATH%
echo    🎨 النوع: اختصار batch محسّن

echo.
echo 🎨 تحسينات الاختصار الجديد:
echo    ✅ واجهة جميلة عند التشغيل
echo    ✅ رسائل واضحة ومفيدة
echo    ✅ معالجة أخطاء محسّنة
echo    ✅ تشغيل سريع وموثوق

echo.
echo 💡 إضافة أيقونة مخصصة (اختياري):

REM التحقق من وجود الأيقونات الجديدة
if exist "youtube_desktop_icon.png" (
    echo ✅ تم العثور على أيقونة سطح المكتب المحسّنة
    echo 🎨 يمكنك تغيير أيقونة الاختصار يدوياً:
    echo    1. انقر بالزر الأيمن على الاختصار
    echo    2. اختر "Properties" أو "خصائص"
    echo    3. اضغط "Change Icon" أو "تغيير الأيقونة"
    echo    4. اختر: youtube_desktop_icon.png
) else (
    echo ⚠️ لم يتم العثور على أيقونة مخصصة
    echo 💡 لإنشاء أيقونة محسّنة، شغّل:
    echo    python create_better_logo.py
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ تم التحديث بنجاح!                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 تم تحديث اختصار سطح المكتب بنجاح!
echo.

echo هل تريد اختبار الاختصار الجديد؟ (y/n)
set /p test_shortcut="اختر: "

if /i "%test_shortcut%"=="y" (
    echo.
    echo 🧪 اختبار الاختصار الجديد...
    call "%NEW_SHORTCUT%"
) else (
    echo.
    echo 💡 يمكنك اختبار الاختصار لاحقاً بالنقر المزدوج عليه
)

echo.
echo 📋 ملخص ما تم:
echo    ✅ حذف الاختصار القديم (إن وجد)
echo    ✅ إنشاء اختصار جديد محسّن
echo    ✅ إضافة واجهة جميلة
echo    ✅ تحسين معالجة الأخطاء

echo.
echo 🙏 شكراً لاستخدام YouTube Video Downloader!
echo.
pause
