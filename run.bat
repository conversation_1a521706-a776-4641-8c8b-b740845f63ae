@echo off
REM YouTube Downloader - Windows Batch Launcher
REM This script provides easy access to the YouTube Downloader on Windows

title YouTube Video Downloader

echo.
echo ========================================
echo    YouTube Video Downloader
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if launcher.py exists
if not exist "launcher.py" (
    echo ERROR: launcher.py not found
    echo Please ensure all files are in the same directory
    echo.
    pause
    exit /b 1
)

REM Run the launcher
echo Starting YouTube Downloader Launcher...
echo.
python launcher.py

echo.
echo YouTube Downloader has been closed.
pause
